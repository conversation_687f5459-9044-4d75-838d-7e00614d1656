<div class="tab-wrapper">
    <div class="standard-table invoices-table">
        <div class="table-wrapper">
            <table>
                <tr>
                    <th class="">Date</th>
                    <th class="">Bill number</th>
                    <th class="">Amount</th>
                    <th class="">Cart</th>
                    <th class="">Invoice</th>
                    <th class="">More Information</th>
                </tr>
                @foreach ($carts->sortByDesc('created_at') as $cart)
                    <tr>
                        <td class="date ">{{ formatDateGregorian($cart->created_at) }}</td>
                        <td class="id ">{{ $cart->id }}</td>
                        <td class="total">
                            @php
                                $pay = $cart->pays()->where('status', 'success')->first();
                                $amount = $pay ? $pay->amount : 0;
                                $amount += $cart->gift_coupon_discount;
                            @endphp
                            <span class="value ">{{ currencyMask($amount  ) }}</span>
                            <span class="currency">€</span>
                        </td>
                        <td class="action"><a href="javascript:void(0)" class="cart-details"
                                              cartId="{{$cart->id}}"> View </a>
                        </td>
                        @php
                            $invoiceRequest = App\Models\Carts\InvoiceRequest::where('cart_id', $cart->id)
                                ->orderByDesc('created_at')
                                ->first();
                        @endphp
                        @if ($invoiceRequest)
                            @if ($invoiceRequest->status == 'accepted')
                                <td class="action">
                                    <div class="c-btn invoice-status download">
                                        <a href="{{ route('front.get.invoice.request', $invoiceRequest->id) }}"
                                           target="_blank">Download Invoice</a>
                                    </div>
                                </td>
                            @elseif($invoiceRequest->status == 'pending')
                                <td class="action">
                                    <div class="c-btn invoice-status pending">Request Pending
                                    </div>
                                </td>
                            @elseif($invoiceRequest->status == 'rejected')
                                <td class="action" data-cart-id="{{ $cart->id }}">
                                    <div class="req-agine" style="display: block">
                                        <div class="c-btn invoice-status denied" data-modal-open="m_invoiceReq"
                                             style="text-decoration: underline">Request Again
                                        </div>

                                    </div>
                                </td>
                            @endif
                        @else
                            <td class="action" data-cart-id="{{ $cart->id }}">
                                <div class="c-btn invoice-req" data-modal-open="m_invoiceReq">
                                    Request Invoice
                                </div>
                            </td>
                        @endif
                        <td>
                            @if($invoiceRequest != null && $invoiceRequest->status == 'rejected')
                                @isset($invoiceRequest->reject_reason)
                                    <div class="reject-reason" style="cursor: pointer"
                                         data-reason="{{$invoiceRequest->reject_reason}}">
                                        Reject Reason
                                    </div>
                                @else
                                    <div>
                                        <div>
                                        </div>
                                    </div>
                                @endisset
                            @else
                                <div>
                                </div>
                            @endif


                        </td>
                    </tr>
                @endforeach
            </table>
        </div>
    </div>
    @include('front.pages.dashboard.components.reject-reason-modal')

    <div class="standard-modal micromodal-slide " id="cart-details" aria-hidden="true">
        <div class="modal__overlay">
            <div class="modal__container" style="max-width: inherit;padding: 0 25px">
                <div class="modal-header">
                    <div class="modal-header-title"></div>
                    <div class="modal-close-bt" data-modal-close=""></div>
                </div>
                <div class="standard-table invoices-table">

                    <div class="table-wrapper" style="width: auto ; min-width: 300px">
                        <table id="bought-list" style="width: auto ; min-width: 290px">

                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

</div>
