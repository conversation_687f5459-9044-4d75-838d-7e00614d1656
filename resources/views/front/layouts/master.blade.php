<!--
Hello Developer :)
Smart Developer always look at the codes.
If you read this probably you are a web developer Or programmer.
tell me if you see something wrong in code or other thing
Best Regard
NoonFont team
-->
<!doctype html>
<html class="no-js" lang="Fa">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title> @yield('title', 'NoonFont') | NoonFont</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="description" content="@yield('description', 'Online arabic and persian font store')">
    <meta name="keywords" content="@yield('keywords', 'NoonFont,Download Font,Arabic Font,Persian Font,Font Store,Font Design,خطوط عربية،خط عربي،arabic font')">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta property="og:title" content="@yield('title', 'NoonFont')">
    <meta property="og:locale" content="en_US">
    <meta property="og:type" content="website">
    <meta property="og:image" content="@yield('share-icon', asset('/front/img/logo-en3.svg'))">
    <meta name="twitter:title" content="@yield('title', 'NoonFont')">
    <meta name="twitter:description" content="@yield('description', 'Online arabic and persian font store')">
    <meta name="twitter:card" content="@yield('share-icon', asset('/front/img/logo-en3.svg'))">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('/front/apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('/front/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('/front/favicon-16x16.png') }}">
    <link rel="manifest" href="{{ asset('/front/site.webmanifest') }}" crossorigin="use-credentials">
    <link rel="mask-icon" href="{{ asset('/front/safari-pinned-tab.svg') }}" color="#FFD500">
    <meta name="msapplication-TileColor" content="#FFD500">
    <meta name="theme-color" content="#000">
    <link rel="stylesheet" href="{{ asset('/front/css/vendor/swiper-bundle.min.css') }}">
    <link rel="stylesheet" href="{{ asset('/front/css/normalize.min.css') }}">
    <link rel="stylesheet" href="{{ asset('/front/css/main.css') }}">
    <link rel="stylesheet" href="{{ asset('/front/css/custom.css') }}">
    <!-- new -->
    <link rel="stylesheet" href="{{ asset('/front/css/new.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@200..800&display=swap" rel="stylesheet">


    <script async src="https://www.googletagmanager.com/gtag/js?id=G-EG8B85M22V"></script>
    <script>
        window.dataLayer = window.dataLayer || [];

        function gtag() {
            dataLayer.push(arguments);
        }
        gtag('js', new Date());

        gtag('config', 'G-EG8B85M22V');
    </script>


    @yield('page-css')

</head>

<body>
    <div class="page @yield('page-class')">
        @include('front.components.header.header')
        <main class="page-content" id="pageContent">
            @yield('content')
        </main>
        @include('front.components.footer.footer')
    </div>

    {{-- Country Restriction Modal --}}
    <div class="standard-modal micromodal-slide" id="country-restriction-modal" aria-hidden="true">
        <div class="modal__overlay" tabindex="-1">
            <div class="modal__container login-modal">
                <div class="modal-header">
                    <div class="modal-header-title">Service Restriction</div>
                </div>
                <div class="modal-content-wrapper form-control small">
                    <div class="main">
                        <button type="button" class="close-modal-bt" data-modal-close data-micromodal-close>×</button>
                        <div class="restriction-message" style="text-align: center; padding: 20px;">
                            <div style="font-size: 18px; margin-bottom: 15px; color: #e74c3c;">
                                <i class="fas fa-exclamation-triangle" style="margin-right: 10px;"></i>
                                Service Not Available
                            </div>
                            <p style="font-size: 14px; line-height: 1.6; color: #666;">
                                We apologize, but our services are currently not available in your country.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div id="recaptchaData" data-sitekey="{{ env('RECAPTCHA_SITEKEY') }}"
        data-sut="{{ \App\Models\Admin\Config\GlobalConfig::where('key', 'recaptcha')->first()?->value }}" data-value>
    </div>

    <script>
        const imageExistRoute = '{{ route('front.image-exists') }}';
    </script>
    <script src="{{ asset('/front/js/vendor/jquery-3.6.0.min.js') }}"></script>
    <script src="{{ asset('/admin/assets/js/axios.min.js') }}"></script>
    {{-- @if (request()->query('show_edit_modal') == 'true'
    //            && \Carbon\Carbon::createFromTimeString(auth()->user()->created_at)->diffInMinutes(\Carbon\Carbon::now()) <= 2)
    @include('front.components.modal.google-login-modal')
@endif --}}
    <script src="{{ asset('/front/js/pages/ImageSizing.min.js') }}"></script>
    <script src="{{ asset('/front/js/vendor/modernizr-3.11.2.min.js') }}"></script>
    <script src="{{ asset('/front/js/vendor/swiper-bundle.min.js') }}"></script>
    <script src="{{ asset('/front/js/vendor/micromodal.min.js') }}"></script>
    <script src="{{ asset('/front/js/vendor/sweetalert2.all.min.js') }}"></script>

    <script src="{{ asset('/front/js/plugins.js') }}"></script>
    <script src="{{ asset('/front/js/main.js') }}?<?php echo date('l jS \of F Y h:i:s A'); ?>"></script>
    <script src="{{ asset('/front/js/vendor/yall.min.js') }}?v=2"></script>
    <script src="{{ asset('/front/js/pages/custom.js') }}?v=2"></script>
    <script>
        document.addEventListener("DOMContentLoaded", yall);
    </script>
    @yield('page-js')

    {{-- Country Restriction Script --}}
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const restrictionShown = localStorage.getItem('country_restriction_shown');
            if (!restrictionShown) {

                const blockedCountries = ['IR', 'US', 'IL', 'PK','FI'];
                const modal = document.getElementById('country-restriction-modal');

                // Function to show the modal
                function showRestrictionModal() {
                    if (modal) {
                        modal.style.display = 'block';
                    }
                }
                
                // Function to close the modal
                function closeRestrictionModal() {
                    if (modal) {
                        modal.style.display = 'none';
                    }
                    localStorage.setItem('country_restriction_shown', 'true');
                }

                // *** NEW: Add event listener to close the modal ***

                // 1. Close when clicking on the background overlay
                if (modal) {
                    modal.addEventListener('click', function(event) {
                        // Check if the clicked element is the modal background itself
                        if (event.target === modal) {
                            closeRestrictionModal();
                        }
                    });
                }

                // 2. Close when pressing the "Escape" key
                window.addEventListener('keydown', function(event) {
                    if (event.key ) {
                        closeRestrictionModal();
                    }
                });


                // Fetch logic remains the same
                fetch('https://ipapi.co/json/')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        return response.json();
                    })
                    .then(data => {
                        const userCountryCode = data.country_code;

                        if (blockedCountries.includes(userCountryCode)) {
                            showRestrictionModal();
                        }
                    })
                    .catch(error => {
                        console.error('Error fetching country data:', error);
                    });
            }
        });
    </script>

    {{-- <script type="text/javascript"> window.$crisp=[];window.CRISP_WEBSITE_ID="00bf82e0-a937-49f8-ae72-9423c2132c25";(function(){ d=document;s=d.createElement("script"); s.src="https://client.crisp.chat/l.js"; s.async=1;d.getElementsByTagName("head")[0].appendChild(s);})(); </script> --}}
</body>

</html>
