<!--
Hello Developer :)
Smart Developer always look at the codes.
If you read this probably you are a web developer Or programmer.
tell me if you see something wrong in code or other thing
Best Regard
NoonFont team
-->
<!doctype html>
<html class="no-js" lang="Fa">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <title> @yield('title' ,'NoonFont') | NoonFont</title>
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="description" content="@yield('description' ,'Online arabic and persian font store')">
    <meta name="keywords"
          content="@yield('keywords' ,'NoonFont,Download Font,Arabic Font,Persian Font,Font Store,Font Design,خطوط عربية،خط عربي،arabic font')">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta property="og:title" content="@yield('title' ,'NoonFont')">
    <meta property="og:locale" content="en_US">
    <meta property="og:type" content="website">
    <meta property="og:image" content="@yield('share-icon' ,asset('/front/img/logo-en3.svg'))">
    <meta name="twitter:title" content="@yield('title' ,'NoonFont')">
    <meta name="twitter:description" content="@yield('description' ,'Online arabic and persian font store')">
    <meta name="twitter:card" content="@yield('share-icon' ,asset('/front/img/logo-en3.svg'))">
    <link rel="apple-touch-icon" sizes="180x180" href="{{ asset('/front/apple-touch-icon.png') }}">
    <link rel="icon" type="image/png" sizes="32x32" href="{{ asset('/front/favicon-32x32.png') }}">
    <link rel="icon" type="image/png" sizes="16x16" href="{{ asset('/front/favicon-16x16.png') }}">
    <link rel="manifest" href="{{ asset('/front/site.webmanifest') }}" crossorigin="use-credentials">
    <link rel="mask-icon" href="{{ asset('/front/safari-pinned-tab.svg') }}" color="#FFD500">
    <meta name="msapplication-TileColor" content="#FFD500">
    <meta name="theme-color" content="#000">
    <link rel="stylesheet" href="{{ asset('/front/css/vendor/swiper-bundle.min.css') }}">
    <link rel="stylesheet" href="{{ asset('/front/css/normalize.min.css') }}">
    <link rel="stylesheet" href="{{ asset('/front/css/main.css') }}">
    <link rel="stylesheet" href="{{ asset('/front/css/custom.css') }}">
    <!-- new -->
    <link rel="stylesheet" href="{{ asset('/front/css/new.css') }}">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Plus+Jakarta+Sans:wght@200..800&display=swap" rel="stylesheet">


    <script async src="https://www.googletagmanager.com/gtag/js?id=G-EG8B85M22V"></script>
    <script>
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);}
    gtag('js', new Date());

    gtag('config', 'G-EG8B85M22V');
    </script>


    @yield('page-css')

</head>
<body>
<div class="page @yield('page-class')">
    @include('front.components.header.header')
    <main class="page-content" id="pageContent">
        @yield('content')
    </main>
    @include('front.components.footer.footer')
</div>
<div id="recaptchaData" data-sitekey="{{ env('RECAPTCHA_SITEKEY') }}"
     data-sut="{{\App\Models\Admin\Config\GlobalConfig::where('key','recaptcha')->first()?->value}}"
     data-value></div>

<script>
    const imageExistRoute = '{{route('front.image-exists')}}';
</script>
<script src="{{ asset('/front/js/vendor/jquery-3.6.0.min.js') }}"></script>
<script src="{{ asset('/admin/assets/js/axios.min.js') }}"></script>
{{--@if(
    request()->query('show_edit_modal') == 'true'
//            && \Carbon\Carbon::createFromTimeString(auth()->user()->created_at)->diffInMinutes(\Carbon\Carbon::now()) <= 2
    )
    @include('front.components.modal.google-login-modal')
@endif--}}
<script src="{{ asset('/front/js/pages/ImageSizing.min.js') }}"></script>
<script src="{{ asset('/front/js/vendor/modernizr-3.11.2.min.js') }}"></script>
<script src="{{ asset('/front/js/vendor/swiper-bundle.min.js') }}"></script>
<script src="{{ asset('/front/js/vendor/micromodal.min.js') }}"></script>
<script src="{{asset('/front/js/vendor/sweetalert2.all.min.js')}}"></script>

<script src="{{ asset('/front/js/plugins.js') }}"></script>
<script src="{{ asset('/front/js/main.js') }}?<?php echo date('l jS \of F Y h:i:s A'); ?>"></script>
<script src="{{ asset('/front/js/vendor/yall.min.js') }}?v=2"></script>
<script src="{{asset('/front/js/pages/custom.js')}}?v=2"></script>
<script>
    document.addEventListener("DOMContentLoaded", yall);
</script>
@yield('page-js')
{{-- <script type="text/javascript"> window.$crisp=[];window.CRISP_WEBSITE_ID="00bf82e0-a937-49f8-ae72-9423c2132c25";(function(){ d=document;s=d.createElement("script"); s.src="https://client.crisp.chat/l.js"; s.async=1;d.getElementsByTagName("head")[0].appendChild(s);})(); </script> --}}
</body>
</html>
