<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>variable axis slider</title>
    <!-- <script src="lottie.js"></script> -->
    <link rel="stylesheet" href="./style.css">

</head>
<body>
  <div class="page-content">
    <div class="page-width">
      <div class="main">
          <!-- <div class="lang-toggle" onclick="toggleLanguage(this)">
            <div class="lang-wrapper">
              <span>FA</span>
              <span>EN</span>
            </div>
          </div> -->
          <div class="preview-wrapper">
            <p class="fvs" id="fvs">کیهان کلهر</p>
          </div>
          <div class="options">
            <div class="sliders bs-slider">
              <div class="wrapper">
                <div class="row">
                  <div class="slider-title">
                    <div class="var-name">Weight<span>: </span></div>
                    <div class="var-value" ><span id="weightSliderValue">400</span></div>
                  </div>
                  <div class="slider-main slider-weight">
                    <input
                    id="weightSlider"
                    type="text"
                    data-slider-min="100"
                    data-slider-max="950"
                    data-slider-value="400"
                    data-slider-step="1"
                    data-slider-ticks="[100, 200, 300, 400, 500, 600, 700, 800, 900, 950]"
                    data-slider-ticks-snap-bounds="8"
                    />
                  </div>
                </div>   
                <div class="row">
                  <div class="slider-title">
                    <div class="var-name">Width<span>: </span></div>
                    <div class="var-value" ><span id="widthSliderValue">0</span></div>
                  </div>
                  <div class="slider-main slider-width">
                    <input
                    id="widthSlider"
                    type="text"
                    data-slider-min="0"
                    data-slider-max="100"
                    data-slider-value="0"
                    data-slider-step="1"
                    data-slider-ticks="[0, 40, 100]"
                    data-slider-ticks-snap-bounds="3"
                    />
                  </div>
                </div>   
              
              </div>
            </div>
            <div class="action-bts">
              <div class="wrapper">
                <button id="" class="action-btn reset-btn" title="بازنشانی" onclick="resetVariationSettings();">
                  <svg xmlns="http://www.w3.org/2000/svg" width="42" height="42" viewBox="0 0 42 42" fill="none">
                    <path d="M38.3045 23.625C37.0379 32.0448 29.7727 38.5 21 38.5C11.335 38.5 3.5 30.6649 3.5 21C3.5 11.335 11.335 3.5 21 3.5C28.176 3.5 34.3434 7.81933 37.0438 14" stroke="currentColor" stroke-width="2.8" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M29.75 14H37.45C38.0299 14 38.5 13.5299 38.5 12.95V5.25" stroke="currentColor" stroke-width="2.8" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
                <button id="randomizer" class="action-btn randomizer-btn" title="تصادفی" onclick="randomizePredefinedValue(); trackRandomizerClicks();">
                  <svg xmlns="http://www.w3.org/2000/svg" width="42" height="42" viewBox="0 0 42 42" fill="none">
                    <path d="M36.75 12.868V29.1325C36.75 29.5138 36.5434 29.865 36.21 30.0503L21.51 38.2169C21.1929 38.3931 20.8072 38.3931 20.4901 38.2169L5.7901 30.0503C5.45678 29.865 5.25004 29.5138 5.25004 29.1325L5.25 12.868C5.25 12.4867 5.45674 12.1354 5.79008 11.9502L20.4901 3.7835C20.8072 3.60732 21.1929 3.60732 21.51 3.7835L36.21 11.9502C36.5432 12.1354 36.75 12.4867 36.75 12.868Z" stroke="currentColor" stroke-width="2.8" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M6.17383 12.764L20.4891 20.7169C20.8062 20.8931 21.1919 20.8931 21.509 20.7169L35.8741 12.7363" stroke="currentColor" stroke-width="2.8" stroke-linecap="round" stroke-linejoin="round"/>
                    <path d="M21 36.75V21" stroke="currentColor" stroke-width="2.8" stroke-linecap="round" stroke-linejoin="round"/>
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M21.0003 10.8008C21.461 10.8008 21.9468 10.9166 22.3383 11.1776C22.7339 11.4414 23.0003 11.8382 23.0003 12.3008C23.0003 12.7634 22.7339 13.1602 22.3383 13.4239C21.9468 13.6849 21.461 13.8008 21.0003 13.8008C20.5396 13.8008 20.0538 13.6849 19.6623 13.4239C19.2667 13.1602 19.0003 12.7634 19.0003 12.3008C19.0003 11.8382 19.2667 11.4414 19.6623 11.1776C20.0538 10.9166 20.5396 10.8008 21.0003 10.8008Z" fill="currentColor"/>
                    <path d="M33.6808 17.6093C34.057 17.8546 34.1855 18.2953 34.17 18.717C34.1541 19.146 33.9961 19.5839 33.7743 19.9511C33.5529 20.3175 33.2398 20.66 32.867 20.8656C32.4962 21.0702 32.0407 21.1426 31.6571 20.8925C31.2809 20.6473 31.1523 20.2066 31.1679 19.7849C31.1837 19.3559 31.3417 18.918 31.5636 18.5508C31.7849 18.1844 32.0981 17.8418 32.4708 17.6362C32.8416 17.4317 33.2972 17.3592 33.6808 17.6093Z" fill="currentColor"/>
                    <path d="M30.0239 23.6617C30.4001 23.9069 30.5287 24.3477 30.5131 24.7694C30.4973 25.1984 30.3393 25.6363 30.1174 26.0035C29.896 26.3699 29.5829 26.7124 29.2101 26.918C28.8393 27.1225 28.3838 27.195 28.0002 26.9449C27.624 26.6997 27.4954 26.2589 27.511 25.8372C27.5268 25.4083 27.6848 24.9703 27.9067 24.6031C28.1281 24.2367 28.4412 23.8942 28.814 23.6886C29.1848 23.4841 29.6403 23.4116 30.0239 23.6617Z" fill="currentColor"/>
                    <path d="M26.367 29.7141C26.7432 29.9593 26.8718 30.4001 26.8562 30.8218C26.8404 31.2507 26.6824 31.6887 26.4605 32.0559C26.2392 32.4222 25.926 32.7648 25.5533 32.9704C25.1825 33.1749 24.7269 33.2474 24.3433 32.9973C23.9671 32.7521 23.8386 32.3113 23.8541 31.8896C23.87 31.4606 24.028 31.0227 24.2498 30.6555C24.4712 30.2891 24.7843 29.9466 25.1571 29.741C25.5279 29.5365 25.9834 29.464 26.367 29.7141Z" fill="currentColor"/>
                    <path d="M8.25834 17.7602C8.63917 17.5059 9.09548 17.5734 9.46849 17.7738C9.84351 17.9753 10.1604 18.3144 10.3858 18.6783C10.6117 19.043 10.7745 19.4792 10.7951 19.9079C10.8153 20.3294 10.6916 20.7716 10.3181 21.021C9.93731 21.2753 9.481 21.2078 9.10799 21.0074C8.73297 20.8059 8.41608 20.4668 8.19069 20.1029C7.96479 19.7382 7.80195 19.302 7.78139 18.8733C7.76118 18.4518 7.88489 18.0096 8.25834 17.7602Z" fill="currentColor"/>
                    <path d="M15.7051 29.7836C16.0859 29.5293 16.5422 29.5968 16.9152 29.7972C17.2903 29.9987 17.6072 30.3377 17.8325 30.7017C18.0584 31.0664 18.2213 31.5025 18.2418 31.9313C18.2621 32.3528 18.1384 32.795 17.7649 33.0443C17.3841 33.2986 16.9278 33.2312 16.5547 33.0308C16.1797 32.8293 15.8628 32.4902 15.6374 32.1263C15.4115 31.7616 15.2487 31.3254 15.2281 30.8966C15.2079 30.4751 15.3316 30.033 15.7051 29.7836Z" fill="currentColor"/>
                    </svg>
                </button>
              </div>
            </div>
            <div class="predefineds">
              <div class="wrapper">
                <div class="section section-weight">
                  <div class="section-title">Weight - وزن</div>
                  <div class="section-content">
                    <div class="btn-group pre-set pre-set-weight">
                      <button class="pd-btn" onclick="weightAxisSlider.setValue(100);updateFontVariationSettings();weightSliderValue.textContent = 100">Thin</button>
                      <button class="pd-btn" onclick="weightAxisSlider.setValue(300);updateFontVariationSettings();weightSliderValue.textContent = 200">Extra Light</button>
                      <button class="pd-btn" onclick="weightAxisSlider.setValue(400);updateFontVariationSettings();weightSliderValue.textContent = 300">Light</button>
                      <button class="pd-btn" onclick="weightAxisSlider.setValue(600);updateFontVariationSettings();weightSliderValue.textContent = 400">Regular</button>
                      <button class="pd-btn" onclick="weightAxisSlider.setValue(700);updateFontVariationSettings();weightSliderValue.textContent = 500">Medium</button>
                      <button class="pd-btn" onclick="weightAxisSlider.setValue(800);updateFontVariationSettings();weightSliderValue.textContent = 600">SemiBold</button>
                      <button class="pd-btn" onclick="weightAxisSlider.setValue(900);updateFontVariationSettings();weightSliderValue.textContent = 700">Bold</button>
                      <button class="pd-btn" onclick="weightAxisSlider.setValue(900);updateFontVariationSettings();weightSliderValue.textContent = 800">Extra Bold</button>
                      <button class="pd-btn" onclick="weightAxisSlider.setValue(900);updateFontVariationSettings();weightSliderValue.textContent = 900">Black</button>
                      <button class="pd-btn" onclick="weightAxisSlider.setValue(950);updateFontVariationSettings();weightSliderValue.textContent = 950">Extra Black</button>
                    </div>
                  </div>
                </div>
                <div class="section section-width">
                  <div class="section-title">Contrast - کنتراست</div>
                  <div class="section-content">
                    <div class="btn-group pre-set pre-set-width">
                      <button class="pd-btn" onclick="widthAxisSlider.setValue(0);updateFontVariationSettings();widthSliderValue.textContent = 0">Low</button>
                      <button class="pd-btn" onclick="widthAxisSlider.setValue(40);updateFontVariationSettings();widthSliderValue.textContent = 40">Mid</button>
                      <button class="pd-btn" onclick="widthAxisSlider.setValue(100);updateFontVariationSettings();widthSliderValue.textContent = 100">High</button>
                    </div>
                  </div>
                </div>


              </div>
            </div>
          </div>
      </div>
    </div>
  </div>








  <script src="./rSlider.min.js"></script>
  <script>
      /**
       * Font Variation Settings Controller
       * This script manages the variable font axis sliders and their interactions
       */
      
      // DOM element references
      const widthSliderValue = document.getElementById("widthSliderValue");
      const weightSliderValue = document.getElementById("weightSliderValue");
      const target = document.getElementById("fvs"); // Text element to apply font variations

      function toggleLanguage(el) {
        el.classList.toggle('active');
        const fvs = document.getElementById('fvs');
        if (fvs.textContent === 'کیهان کلهر') {
          fvs.textContent = 'Kayhan Kalhor';
        } else {
          fvs.textContent = 'کیهان کلهر';
        }
      }
      /**
       * Weight Axis Slider Configuration
       * Controls the font weight from thin (100) to extra black (950)
       */
      const weightAxisSlider = new Slider("#weightSlider", {
        tooltip: 'hide',
        labelledby: 'weightHandle',
      });
      
      // Initialize active button for weight
      updateActiveButtonWeight(400);
      
      // Event handlers for weight slider
      weightAxisSlider.on("slide", function(sliderValue) {
        weightSliderValue.textContent = weightAxisSlider.getValue();
        updateFontVariationSettings();
        updateActiveButtonWeight(weightAxisSlider.getValue());
      });
      
      weightAxisSlider.on("change", function(sliderValue) {
        weightSliderValue.textContent = weightAxisSlider.getValue();
        updateFontVariationSettings();
        updateActiveButtonWeight(weightAxisSlider.getValue());
      });
      
      /**
       * Updates the active state of weight preset buttons based on current value
       * @param {number} value - The current weight value
       */
      function updateActiveButtonWeight(value) {
        document.querySelectorAll('.pre-set-weight .pd-btn').forEach(btn => {
          // Extract the weight value from the onclick attribute
          const onclickAttr = btn.getAttribute('onclick');
          const weightMatch = onclickAttr && onclickAttr.match(/setValue\((\d+)\)/);
          const weightValue = weightMatch ? parseInt(weightMatch[1]) : null;
          
          btn.classList.toggle('active', weightValue === value);
        });
      }
      
      // Add visual feedback during slider interaction
      weightAxisSlider.on("slideStart", function() {
        const targetDiv = document.querySelector('[aria-labelledby="weightHandle"]');
        if(targetDiv) targetDiv.classList.add('in-use');
      });
      
      weightAxisSlider.on("slideStop", function() {
        const targetDiv = document.querySelector('[aria-labelledby="weightHandle"]');
        if(targetDiv) targetDiv.classList.remove('in-use');
      });

      /**
       * Width Axis Slider Configuration
       * Controls the font width from extra condensed (62.5) to extra expanded (150)
       */
      const widthAxisSlider = new Slider("#widthSlider", {
        tooltip: 'hide',
        labelledby: 'widthHandle',
      });
      
      // Initialize active button for width
      updateActiveButtonWidth(100);
      
      // Event handlers for width slider
      widthAxisSlider.on("slide", function(sliderValue) {
        widthSliderValue.textContent = widthAxisSlider.getValue();
        updateFontVariationSettings();
        updateActiveButtonWidth(widthAxisSlider.getValue());
      });
      
      widthAxisSlider.on("change", function(sliderValue) {
        widthSliderValue.textContent = widthAxisSlider.getValue();
        updateFontVariationSettings();
        updateActiveButtonWidth(widthAxisSlider.getValue());
      });
      
      /**
       * Updates the active state of width preset buttons based on current value
       * @param {number} value - The current width value
       */
      function updateActiveButtonWidth(value) {
        document.querySelectorAll('.pre-set-width .pd-btn').forEach(btn => {
          // Extract the width value from the onclick attribute
          const onclickAttr = btn.getAttribute('onclick');
          const widthMatch = onclickAttr && onclickAttr.match(/setValue\((\d+\.?\d*)\)/);
          const widthValue = widthMatch ? parseFloat(widthMatch[1]) : null;
          
          btn.classList.toggle('active', widthValue === value);
        });
      }
      
      // Add visual feedback during slider interaction
      widthAxisSlider.on("slideStart", function() {
        const targetDiv = document.querySelector('[aria-labelledby="widthHandle"]');
        if(targetDiv) targetDiv.classList.add('in-use');
      });
      
      widthAxisSlider.on("slideStop", function() {
        const targetDiv = document.querySelector('[aria-labelledby="widthHandle"]');
        if(targetDiv) targetDiv.classList.remove('in-use');
      });



      /**
       * Updates the font variation settings based on current slider values
       * Applies the CSS variable font settings to the target element
       */
      function updateFontVariationSettings() {
        target.setAttribute(
          "style",
          `font-variation-settings: "wght" ${weightAxisSlider.getValue()}, "CNTR" ${widthAxisSlider.getValue()};`
        );
      }
      
      // Initial update
      updateFontVariationSettings();

      /**
       * Resets all sliders to their default values
       * Weight: 400 (Regular), Width: 100 (Normal)
       */
      function resetVariationSettings() {
        weightAxisSlider.setValue(400);
        widthAxisSlider.setValue(100);
        weightSliderValue.textContent = 400;
        widthSliderValue.textContent = 100;
        updateFontVariationSettings();
        updateActiveButtonWeight(400);
        updateActiveButtonWidth(100);
      }
      
      // Make reset function globally available
      window.resetVariationSettings = resetVariationSettings;

      /**
       * Randomizes all font variation settings
       * Selects random values from predefined arrays for each axis
       */
      function randomizePredefinedValue() {
        const weights = [100, 200, 300, 400, 500, 600, 700, 800, 900, 950];
        const widths = [0, 40, 100];
        
        // Select random values from each array
        const wght = weights[Math.floor(Math.random() * weights.length)];
        const wdth = widths[Math.floor(Math.random() * widths.length)];
        
        // Apply the random values
        weightAxisSlider.setValue(wght);
        widthAxisSlider.setValue(wdth);
        
        // Update display values
        weightSliderValue.textContent = wght;
        widthSliderValue.textContent = wdth;
        
        // Update font settings and button states
        updateFontVariationSettings();
        updateActiveButtonWeight(wght);
        updateActiveButtonWidth(wdth);
      }
      
      // Make randomizer function globally available
      window.randomizePredefinedValue = randomizePredefinedValue;

      // Note: Pre-set buttons already use setValue and updateFontVariationSettings in their onclick attributes
  </script>
  <script>
      /**
       * Button Group Interaction Handler
       * Manages the active state of buttons within button groups
       * Note: This is redundant as the active state is already managed by the slider functions,
       * but kept for additional robustness
       */
      const btnGroups = document.querySelectorAll('.btn-group');
      
      btnGroups.forEach((group) => {
        const buttons = group.querySelectorAll('.pd-btn');
        buttons.forEach((button) => {
          button.addEventListener('click', () => {
            // Remove active class from all buttons in the group
            buttons.forEach((btn) => {
              btn.classList.remove('active');
            });
            // Add active class to the clicked button
            button.classList.add('active');
          });
        });
      });
  </script>
  <script>
      /**
       * Intersection Observer for Slider Animation
       * Adds 'in-view' class to sliders when they become visible in the viewport
       * This creates a nice animation effect when scrolling to the sliders
       */
      const sliderElements = document.querySelectorAll('.sliders');
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('in-view');
          }
        });
      }, { threshold: 0.5 }); // Trigger when slider is 50% visible
      
      // Observe all slider elements
      sliderElements.forEach(slider => {
        observer.observe(slider);
      });
  </script>

</body>
</html>
