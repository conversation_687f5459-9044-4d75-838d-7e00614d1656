@font-face {
  font-family: 'IRANYekanX';
  font-style: normal;
  font-weight: 400;
  src:  url("./IRANYekanX-Regular.woff2") format("woff2"),
        url("./IRANYekanX-Regular.woff") format("woff");
}
@font-face {
  font-family: 'IRANYekanX';
  font-style: normal;
  font-weight: 300;
  src:  url("./IRANYekanX-Light.woff2") format("woff2"),
        url("./IRANYekanX-Light.woff") format("woff");
}
@font-face {
  font-family: YekanBakh;
  src:  url("./OrangutanVF.woff2") format("woff2"),
        url("./OrangutanVF.woff") format("woff");
}

:root {
    --c-bg: #fff;
    --handle-size: 30px;
    --track-hight: 5px;
    --track-color: #D3D3D3;
}
/* css reset */
progress,sub,sup{vertical-align:baseline}[type=checkbox],[type=radio],legend{box-sizing:border-box;padding:0}details,hr{display:block}fieldset,hr,legend,ol,ul{padding:0}legend,table{max-width:100%}audio,canvas,iframe,img,svg,video{vertical-align:middle;display:block;max-width:100%;border-style:none}button,input,optgroup,select,textarea{font-family:inherit;font-size:100%;margin:0}p{margin-block-start:0.5rem;margin-block-end:0.5rem;font-size:var(--fs-text);color:var(--c-text)}h1{font-size:2rem;margin:.67em 0}hr{height:1px;border:0;border-top:1px solid #ccc;margin:1em 0}a{background-color:transparent;color:var(--c-blue-base)}abbr[title]{border-bottom:none;text-decoration:underline;text-decoration:underline dotted}b,strong{font-weight:bolder}code,kbd,samp{font-family:monospace,monospace;font-size:1em}sub,sup{font-size:75%;line-height:0;position:relative}sub{bottom:-.25em}sup{top:-.5em}button,input{overflow:visible}button,select{text-transform:none}[type=button],[type=reset],[type=submit],button{-webkit-appearance:button}[type=button]::-moz-focus-inner,[type=reset]::-moz-focus-inner,[type=submit]::-moz-focus-inner,button::-moz-focus-inner{border-style:none;padding:0}[type=button]:-moz-focusring,[type=reset]:-moz-focusring,[type=submit]:-moz-focusring,button:-moz-focusring{outline:ButtonText dotted 1px}fieldset{border:0;margin:0}legend{color:inherit;display:table;white-space:normal}textarea{overflow:auto;resize:vertical}[type=number]::-webkit-inner-spin-button,[type=number]::-webkit-outer-spin-button{height:auto}[type=search]{-webkit-appearance:textfield;outline-offset:-2px}[type=search]::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}summary{display:list-item}[hidden],template{display:none}::placeholder{opacity:.6;font-weight:300}ol,ul{list-style:none;margin:0}table{border-collapse:collapse;border-spacing:0px}table,td,th{padding:var(--s-rem-4);border:1px solid var(--c-gray-400)}td,th{text-overflow:ellipsis}

html {
    scroll-behavior: smooth;
}
body {
    font-family: IRANYekanX, tahoma;
    margin: 0;
    padding: 0;
    line-height: 1.4;
    background-color: var(--c-bg);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}
.fvs {
  font-family: YekanBakh;
}
.page-content {
    max-width: 100%;
    overflow: hidden;
}
.page-width {
    margin: 0 auto;
    width: 100%;
    max-width: 1190px;
}
.main {
    background-color: #F8F8F8;
    border-radius: 24px;
    border: 1px solid #EEEEEE;
    padding: 16px;
    position: relative;
}
.preview-wrapper {
  padding: 32px 0 48px 0;
}
.preview-wrapper p {
  font-size: 42px;
  text-align: center;
}
.options .sliders {
  max-width: 800px;
  margin: 0 auto;
}
.options .row {
  display: block;
  padding-bottom: 24px;
}
.options .slider-title {
  width: auto;
  display: flex;
  justify-content: center;
  margin: 0 auto;
  gap: 5px;
}
.options .var-name {
  font-weight: 300;
}
.options .slider-main {
  display: flex;
  justify-content: center;
}
.options .predefineds {
  padding: 16px 0;
}
.options .predefineds > .wrapper {
  max-width: 800px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  border-radius: 16px;
  border: 1px solid #DFDFDF;
}
.predefineds .section {
  width: calc(100% / 1);
  border-bottom: 1px solid #DFDFDF;
  padding: 28px 16px;
  box-sizing: border-box;
}
.predefineds .section:last-child {
  border-color: transparent;
}
.predefineds .section .section-title {
  text-align: center;
  padding-bottom: 16px;
}
.predefineds .btn-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  justify-content: center;
}
.predefineds .pd-btn {
  display: block;
  outline: none;
  border: 1px solid transparent;
  background-color: #EFF0F3;
  color: #3F3F46;
  border-radius: 999px;
  padding: 0 12px;
  cursor: pointer;
  height: 28px;
  line-height: 28px;
  font-size: 13px;
  transition: all 150ms ease-in-out;
}
.predefineds .pd-btn:hover {
  background-color: #dddee1;
}
.predefineds .pd-btn.active {
  background-color: #3C3C3E;
  color: #EFF0F3;
}
.action-bts > .wrapper {
    display: flex;
    justify-content: center;
    gap: 16px;
    padding-bottom: 16px;
}
.action-bts .action-btn {
  width: 56px;
  height: 56px;
  cursor: pointer;
  border-radius: 9999px;
  border: 2px solid var(--track-color);
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: all 150ms ease-in-out;
}
.action-bts .action-btn svg {
  max-width: 100%;
  max-height: 100%;
  color: #3C3C3E;

  transition: all 150ms ease-in-out;
}
.action-bts .action-btn:hover {
  border: 2px solid #9e9e9e;
}
.action-bts .action-btn.reset-btn svg {
  width: 28px;
}
.action-bts .action-btn.randomizer-btn svg {
  width: 30px;

}


/* slider styles */
.bs-slider .slider {
    display: inline-block;
    vertical-align: middle;
    position: relative
}
.bs-slider .slider.slider-horizontal {
    width: calc(100% - 30px);
    max-width: 1190px;
    height: var(--handle-size)
}
.bs-slider .slider.slider-horizontal .slider-track {
    height: var(--track-hight);
    width: 100%;
    margin-top: calc((var(--track-hight) / 2) * -1);
    top: 50%;
    left: 0;
    z-index: 2;
}
.bs-slider .slider.slider-horizontal .slider-selection,
.bs-slider .slider.slider-horizontal .slider-track-low,
.bs-slider .slider.slider-horizontal .slider-track-high {
    height: 100%;
    top: 0;
    bottom: 0
}
.bs-slider .slider.slider-horizontal .slider-tick,
.bs-slider .slider.slider-horizontal .slider-handle {
    margin-left: -15px
}
.bs-slider .slider.slider-horizontal .slider-tick-container {
    white-space: nowrap;
    position: absolute;
    top: 0;
    left: 0;
    width: 100%
}
.bs-slider .slider.slider-horizontal .slider-tick-label-container {
    white-space: nowrap;
    margin-top: var(--handle-size);
}
.bs-slider .slider.slider-horizontal .slider-tick-label-container .slider-tick-label {
    display: inline-block;
    text-align: center
}
.bs-slider .slider.slider-disabled .slider-handle {
    background-color: #cfcfcf;
}
.bs-slider .slider.slider-disabled .slider-track {
    background-color: #e7e7e7;
    cursor: not-allowed
}
.bs-slider .slider input {
    display: none
}
.bs-slider .slider .tooltip {
    position: absolute;
    top: 30px;
}
.bs-slider .slider .tooltip-inner {
    white-space: nowrap;
    max-width: none
}
.bs-slider .slider .bs-tooltip-top .tooltip-inner,
.bs-slider .slider .bs-tooltip-bottom .tooltip-inner {
    position: relative;
    left: -50%
}
.bs-slider .slider .tooltip {
    pointer-events: none
}
.bs-slider .slider .tooltip.bs-tooltip-top {
    margin-top: -44px;
}
.bs-slider .slider .hide {
    display: none;
}
.bs-slider .slider-track {
    background-color: var(--track-color);
    border-radius: 9999px;
    position: absolute;
    cursor: pointer;
}
.bs-slider .slider-selection {
    background-color: var(--track-color);
    box-sizing: border-box;
    border-radius: 9999px;
    position: absolute;
}
.bs-slider .slider-selection.tick-slider-selection {
    background-color: var(--track-color);
}
.bs-slider .slider-track-low,
.bs-slider .slider-track-high {
    box-sizing: border-box;
    border-radius: 9999px;
    position: absolute;
    background: transparent;
}
.bs-slider .slider-handle {
    background-color: transparent;
    border-radius: 9999px;
    position: absolute;
    top: 0;
    width: var(--handle-size);
    height: var(--handle-size);
    z-index: 3;
    box-sizing: border-box;
    padding: 2.5px;
    outline: none;
}
.bs-slider .slider-handle .slider-handle-inner {
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  border-radius: 9999px;
  border: 2px solid var(--track-color);
  background-color: #fff;
  transition: all 75ms ease-out;
}
.bs-slider .slider-handle:hover {
    cursor: pointer;
}
.bs-slider .slider-handle:hover .slider-handle-inner {
  background-color: rgba(255, 255, 255, 1);
  box-shadow:  0px 0px 0px 2px #C1C2C3;
  border-color: transparent;
}
.bs-slider .slider-handle.in-use .slider-handle-inner {
  background-color: rgba(255, 255, 255, 1);
  box-shadow:  0px 0px 0px 2px #C1C2C3;
  border-color: transparent;
}
.bs-slider .slider-tick {
    background-color: transparent;
    box-sizing: border-box;
    position: absolute;
    cursor: pointer;
    width: var(--handle-size);
    height: var(--handle-size);
    padding: 6px;
}
.bs-slider .slider-tick .slider-tick-inner {
    position: relative;
    width: 100%;
    height: 100%;
}
.bs-slider .slider-tick .slider-tick-inner::before {
    content: '';
    position: absolute;
    margin: auto;
    left: 0;
    right: 0;
    width: 3px;
    height: 100%;
    background-color: var(--track-color);
    border-radius: 9999px;
}
.lang-toggle {
  position: absolute;
  top: 18px;
  right: 18px;
  display: none;
}
.lang-toggle .lang-wrapper {
  position: relative;
  display: flex;
  gap: 8px;
  padding: 8px;
  background-color: #e7e7e7;
  border-radius: 12px;
  cursor: pointer;
  transition: all 250ms ease-in-out;
}
.lang-toggle .lang-wrapper::after {
  content: '';
  position: absolute;
  height: 32px;
  width: 32px;
  top: 7px;
  left: 7px;
  background-color: #fff;
  box-shadow: 0px 3px 8px -3px rgba(0, 0, 0, 0.2);
  border: 1px solid #d3d3d3;
  border-radius: 8px;
  transition: all 250ms ease-in-out;
}
.lang-toggle.active .lang-wrapper::after {
  transform: translateX(40px);
}
.lang-toggle span {
  z-index: 3;
  position: relative;
  display: block;
  height: 32px;
  width: 32px;
  line-height: 36px;
  text-align: center;
  transition: all 250ms ease-in-out;
}
.lang-toggle .lang-wrapper span:first-child {
    color: #000;
}
.lang-toggle .lang-wrapper span:last-child {
    color: #818181;

}
.lang-toggle.active .lang-wrapper span:first-child {
    color: #818181;

}
.lang-toggle.active .lang-wrapper span:last-child {
    color: #000;

}

/* media queries */
@media only screen and (min-width: 359px) {
  .preview-wrapper p {
    font-size: 54px;
  }
}
@media only screen and (min-width: 579px) {
  .preview-wrapper p {
    font-size: 68px;
  }
}
@media only screen and (min-width: 769px) {
  
  .preview-wrapper p {
    font-size: 78px;
  }
  .lang-toggle {
    display: block;
  }
}
@media only screen and (min-width: 920px) {
    .preview-wrapper p {
      font-size: 148px;
    }
    .preview-wrapper {
      padding: 48px 0 56px 0;
    }
    .options .predefineds > .wrapper {
      flex-direction: row;
    }
    .predefineds .section {
      width: calc(100% / 3);
      border-bottom: none;
      border-right: 1px solid #DFDFDF;
    }
    .predefineds .btn-group {
      justify-content: flex-start;
    }
    .options .row {
      display: flex;
      align-items: center;
      padding-bottom: 32px;
    }
    .options .slider-title {
      justify-content: start;
      width: 90px;
    }
    .options .slider-main {
      width: calc(100% - 90px);
      transition: all 400ms ease-out;
      }
    .sliders.in-view .wrapper .row:nth-child(1) .slider-handle {
      -webkit-animation: shake-horizontal 1500ms cubic-bezier(0.455, 0.030, 0.515, 0.955) 1 both;
      animation: shake-horizontal 1500ms cubic-bezier(0.455, 0.030, 0.515, 0.955) 1 both;
    }
    .sliders.in-view .wrapper .row:nth-child(2) .slider-handle {
      -webkit-animation: shake-horizontal-r 1500ms cubic-bezier(0.455, 0.030, 0.515, 0.955) 1 both;
      animation: shake-horizontal-r 1500ms cubic-bezier(0.455, 0.030, 0.515, 0.955) 1 both;
    }
    .sliders.in-view .wrapper .row:nth-child(3) .slider-handle {
      -webkit-animation: shake-horizontal 1500ms cubic-bezier(0.455, 0.030, 0.515, 0.955) 1 both;
      animation: shake-horizontal 1500ms cubic-bezier(0.455, 0.030, 0.515, 0.955) 1 both;
    }
    .options .predefineds {
      display: block;
    }
    .action-bts > .wrapper {
      padding: 16px 0 42px 0;
    }
    .canvas {
      display: none;
      position: absolute;
      z-index: 20;
      bottom: 264px;
      right: 418px;
      cursor: wait;
    }
    .easter-egg .canvas {
      display: block;
    }
    .easter-egg .action-bts .action-btn.randomizer-btn {
 cursor: wait;
}
.easter-egg .action-bts .action-btn.randomizer-btn svg {
  opacity: 0;
}
    /* qqq */
}

/**
 * ----------------------------------------
 * animation shake-horizontal
 * ----------------------------------------
 */
 @-webkit-keyframes shake-horizontal {
  0%,
  100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70% {
    -webkit-transform: translateX(-5px);
            transform: translateX(-5px);
  }
  20%,
  40%,
  60% {
    -webkit-transform: translateX(5px);
            transform: translateX(5px);
  }
  80% {
    -webkit-transform: translateX(4px);
            transform: translateX(4px);
  }
  90% {
    -webkit-transform: translateX(-4px);
            transform: translateX(-4px);
  }
}
@keyframes shake-horizontal {
  0%,
  100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70% {
    -webkit-transform: translateX(-5px);
            transform: translateX(-5px);
  }
  20%,
  40%,
  60% {
    -webkit-transform: translateX(5px);
            transform: translateX(5px);
  }
  80% {
    -webkit-transform: translateX(4px);
            transform: translateX(4px);
  }
  90% {
    -webkit-transform: translateX(-4px);
            transform: translateX(-4px);
  }
}

@-webkit-keyframes shake-horizontal-r {
  0%,
  100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70% {
    -webkit-transform: translateX(5px);
            transform: translateX(5px);
  }
  20%,
  40%,
  60% {
    -webkit-transform: translateX(-5px);
            transform: translateX(-5px);
  }
  80% {
    -webkit-transform: translateX(-4px);
            transform: translateX(-4px);
  }
  90% {
    -webkit-transform: translateX(4px);
            transform: translateX(4px);
  }
}
@keyframes shake-horizontal-r {
  0%,
  100% {
    -webkit-transform: translateX(0);
            transform: translateX(0);
  }
  10%,
  30%,
  50%,
  70% {
    -webkit-transform: translateX(5px);
            transform: translateX(5px);
  }
  20%,
  40%,
  60% {
    -webkit-transform: translateX(-5px);
            transform: translateX(-5px);
  }
  80% {
    -webkit-transform: translateX(-4px);
            transform: translateX(-4px);
  }
  90% {
    -webkit-transform: translateX(4px);
            transform: translateX(4px);
  }
}