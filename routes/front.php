<?php


use App\Http\Controllers\Front\Auth\AuthController;
use App\Http\Controllers\Front\License\LicenseController;
use App\Http\Controllers\Front\Page\PageController;
use App\Http\Controllers\Front\Portfolio\PortfolioController;
use App\Http\Controllers\Front\User\UserPaymentInformationController;
use App\Http\Controllers\Utils\UtilController;
use App\Models\Faq\Faq;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Front\Carts\CartController;
use App\Http\Controllers\Front\Designers\DesignerController;
use App\Http\Controllers\Front\Faq\FaqController;
use App\Http\Controllers\Front\Font\CommentController;
use App\Http\Controllers\Front\Font\FontController;
use App\Http\Controllers\Front\Index\IndexController;
use App\Http\Controllers\Front\Search\SearchController;
use App\Http\Controllers\Front\Tag\TagController;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\RateLimiter;
use App\Http\Controllers\Front\Affiliate\AffiliateController;
use App\Http\Controllers\Front\Carts\GiftCartController;
use App\Http\Controllers\Front\Carts\PayController;
use App\Http\Controllers\Front\Dashboard\DashboardController;
use App\Http\Controllers\Front\User\UserController;
use App\Http\Middleware\VerifyCsrfToken;

Route::get('/about', function () {
    return view('front.pages.about-us.index');
})->name('about-us');
Route::get('/contactus', function () {
    $faqs = Faq::where('show_in_main_page', '1')->orderBy('priority', 'DESC')->get();
    return view('front.pages.contact-us.index', compact('faqs'));
})->name('contact-us');


Route::get('/page/{slug}', [PageController::class, 'index'])->name('page');

Route::get('/about-licenses', [LicenseController::class, 'licenseDetailsPage'])->name('license.about');

Route::prefix('/license/')->name('license.')->group(function () {
    Route::get('/{code}', [LicenseController::class, 'show'])->name('show');
    Route::get('/', [LicenseController::class, 'index'])->name('index');
    Route::post('/', [LicenseController::class, 'index'])->name('search');
});

Route::post('/image-exists', [UtilController::class, 'imageExists'])->name('image-exists');
//----------------------------------------MILAD----------------------------------------------
RateLimiter::for('font_lab', function (Request $request) {
    return $request->user()
        ? Limit::perMinute(13)->by($request->user()->id)
        : Limit::perMinute(13)->by(getIp())
            ->response(function () {
                return response('Your request exceeds the limit.!', 429);
            });
});
RateLimiter::for('comments', function (Request $request) {
    return $request->user()
        ? Limit::perMinute(8)->by($request->user()->id)
        : Limit::perMinute(8)->by(getIp())
            ->response(function () {
                return response('Your request exceeds the limit.!', 429);
            });
});

Route::get('/', [IndexController::class, 'index'])->name('index');

Route::get('/search', [SearchController::class, 'search'])->name('search');

Route::prefix('/fonts')->name('fonts')->group(function () {
    Route::get('/', [FontController::class, 'index']);
    Route::prefix('/category')->name('.category')->group(function () {
        Route::get('/{slug?}', [FontController::class, 'index'])->name('.single');
    });
    Route::get('/updates/{slug}', [FontController::class, 'getFontUpdate'])->name('.font_updates_single');
    Route::prefix('/comments')->name('.comments')->group(function () {
        Route::get('/{slug}/comments', [FontController::class, 'getComments'])->name('.all');
        Route::middleware(['throttle:font_lab', 'auth'])->post('/comments', [CommentController::class, 'store'])->name('.store');
    });


    Route::post('/filter', [FontController::class, 'filterFonts'])->name('.filter');
    Route::post('/index-page/filter', [IndexController::class, 'indexPageFilterFont'])->name('.index-page.filter');

    Route::get('/latest', [IndexController::class, 'latestFonts'])->name('.latest');

    Route::middleware(['throttle:font_lab'])->get('/generate-font-lab', [FontController::class, 'generateFontLabImages'])->name('.generate_font_lab');
    Route::get('/{slug}', [FontController::class, 'single'])->name('.single');
});


Route::prefix('/designers')->name('designers')->group(function () {
    Route::get('/', [DesignerController::class, 'index'])->name('.all');
    Route::get('/get-all', [DesignerController::class, 'getDesigners'])->name('.get-all');
});

Route::get('/designer/{slug}', [DesignerController::class, 'single'])->name('designers.single');

Route::prefix('/faq')->name('faq')->group(function () {
    Route::get('/', [FaqController::class, 'search'])->name('.search');
});

Route::prefix('/tags')->name('tags')->group(function () {
    Route::get('/search', [TagController::class, 'search'])->name('.search');
    Route::get('/{tag}', [TagController::class, 'show'])->name('.single');
});


RateLimiter::for('cart-details', function (Request $request) {
    return $request->user()
        ? Limit::perMinute(30)->by($request->user()->id)
        : Limit::perMinute(30)->by(getIp())
            ->response(function () {
                return response('Your request exceeds the limit.!', 429);
            });
});
RateLimiter::for('add-to-cart', function (Request $request) {
    return $request->user()
        ? Limit::perMinute(20)->by($request->user()->id)
        : Limit::perMinute(20)->by(getIp())
            ->response(function () {
                return response('Your request exceeds the limit.!', 429);
            });
});
RateLimiter::for('login', function (Request $request) {
    return $request->user()
        ? Limit::perMinute(10)->by($request->user()->id)
        : Limit::perMinute(10)->by(getIp())
            ->response(function () {
                return response('Your request exceeds the limit.!', 429);
            });
});
RateLimiter::for('sign-up', function (Request $request) {
    return $request->user()
        ? Limit::perMinute(16)->by($request->user()->id)
        : Limit::perMinute(16)->by(getIp())
            ->response(function () {
                return response('Your request exceeds the limit.!', 429);
            });
});
RateLimiter::for('code-check', function (Request $request) {
    return $request->user()
        ? Limit::perMinute(20)->by($request->user()->id)
        : Limit::perMinute(20)->by(getIp())
            ->response(function () {
                return response('Your request exceeds the limit.!', 429);
            });
});

RateLimiter::for('password-reset', function (Request $request) {
    return $request->user()
        ? Limit::perMinute(8)->by($request->user()->id)
        : Limit::perMinute(8)->by(getIp())
            ->response(function () {
                return response('Your request exceeds the limit.!', 429);
            });
});

RateLimiter::for('verify-email-phone', function (Request $request) {
    return $request->user()
        ? Limit::perMinute(12)->by($request->user()->id)
        : Limit::perMinute(12)->by(getIp())
            ->response(function () {
                return response('Your request exceeds the limit.!', 429);
            });
});

RateLimiter::for('affiliate-request', function (Request $request) {
    abort_unless(auth()->check(), 403);
    return Limit::perMinute(1)->by($request->user()->id)
        ->response(function () {
            return response('Your request exceeds the limit.!', 429);
        });
});


//dashboard
Route::get('/account', [DashboardController::class, 'dashboardPage'])->name('dashboard');
Route::get('/response', [DashboardController::class, 'responseTime'])->name('response');
Route::get('/account/affiliate', [AffiliateController::class, 'index'])->name('affiliate.dashboard');
Route::get('/account/signup', [AffiliateController::class, 'signup'])->name('affiliate.signup');
Route::post('/account/signup/submit', [AffiliateController::class, 'submitSignup'])->name('affiliate.signup.submit');

Route::post('/account/affiliate/update', [AffiliateController::class, 'updateDetails'])->name('affiliate.update');
Route::get('/account/affiliate/statistics', [AffiliateController::class, 'getStatistics'])->name('affiliate.statistics');
Route::middleware(['throttle:affiliate-request'])->get('/account/affiliate/pay-request', [AffiliateController::class, 'payRequest'])->name('affiliate.pay-request');
Route::post('/account/affiliate/update-coupon', [AffiliateController::class, 'updateCoupon'])->name('affiliate.update-coupon');
Route::get('/account/affiliate/sales-report', [AffiliateController::class, 'salesReport'])->name('affiliate.sales-report');

Route::get('/account/{id}/download', [DashboardController::class, 'downloadFont'])->name('dashboard.download.font');
Route::get('/account/{id}/check', [DashboardController::class, 'checkToDownloadFont'])->name('dashboard.check.font');
Route::post('/account/update/subscriptions', [UserController::class, 'updateSubscriptions'])->name('dashboard.update.subscriptions');
Route::post('/account/send-invoice-request/natural', [DashboardController::class, 'makeNaturalInvoiceRequest'])->name('send.natural.invoice.request');
Route::post('/account/send-invoice-request/legal', [DashboardController::class, 'makeLegalInvoiceRequest'])->name('send.legal.invoice.request');
Route::get('/account/get-invoice-request/{invoiceRequest}', [DashboardController::class, 'getInvoice'])->name('get.invoice.request');
Route::post('/account/update-license-info/{customerLicenseInfo}', [DashboardController::class, 'updateCustomerLicenseInfo'])->name('update.license.info');
Route::middleware(['auth'])->get('/account/cart-details', [DashboardController::class, 'getCartDetails'])->name('dashboard.get-cart-details');

//FAQ
Route::get('/faq', [FaqController::class, 'index'])->name('faq');
Route::get('/faq-search', [FaqController::class, 'search'])->name('faq.search');
//account edit
Route::get('/account/edit', [UserController::class, 'editProfile'])->name('profile.edit');
Route::post('/account/edit', [UserController::class, 'updateUser'])->name('user.update');
//password edit
Route::get('/account/edit-password', [UserController::class, 'editPassword'])->name('password.edit');
Route::post('/account/edit-password', [UserController::class, 'UpdatePassword'])->name('password.update');

Route::get('/account/email/edit', [UserController::class, 'editEmail'])->name('profile.email.edit');
Route::post('/account/email/edit/code', [UserController::class, 'sendEditEmailCode'])->middleware(['throttle:verify-email-phone'])->name('profile.email.update.code');
Route::post('/account/email/edit', [UserController::class, 'updateEmail'])->middleware(['throttle:verify-email-phone'])->name('profile.email.update');


//auth
Route::get('/auth/google', [AuthController::class, 'googleAuth'])->name('auth.google');
Route::get('/google-login', [AuthController::class, 'googleLogin']);
Route::post('/account/google-edit', [AuthController::class, 'googleModalEdit'])->name('user.google-edit');
//login and logout
Route::get('/login', [AuthController::class, 'loginPage'])->name('login.page');
Route::middleware(['throttle:login'])->post('/login', [AuthController::class, 'login'])->name('login');
Route::middleware(['throttle:login'])->post('/login-api', [AuthController::class, 'loginApi'])->name('login.api');
Route::get('/logout', [AuthController::class, 'logout'])->name('logout');

//signup
Route::get('/signup', [AuthController::class, 'signupPage'])->name('signup.page');
Route::middleware(['throttle:code-check'])->get('/signup/code-check', [AuthController::class, 'signupCodeCheck'])->name('signup.code.check');
Route::middleware(['throttle:code-check'])->get('/signup/email-check', [AuthController::class, 'emailExists'])->name('email.exists');
Route::middleware(['throttle:code-check'])->get('/signup/phone-check', [AuthController::class, 'phoneExists'])->name('phone.exists');
Route::middleware(['throttle:sign-up'])->post('/signup', [AuthController::class, 'signup'])->name('signup');
Route::middleware(['throttle:sign-up'])->post('/signup-validate', [AuthController::class, 'validateSignUpData'])->name('signup.validate');
Route::middleware(['throttle:code-check'])->post('/signup/send-email', [AuthController::class, 'sendSignupEmail'])->name('signup.send.email');

//two-factor
Route::prefix('/two-factor')->name('two-factor.')->group(function () {
    Route::prefix('/global')->name('global.')->group(function () {
        Route::get('/', [AuthController::class, 'showGlobalTwoFactorView'])->name('index');
        Route::post('/reset', [AuthController::class, 'resendGlobalTwoFactor'])->name('resend');
        Route::post('/verify', [AuthController::class, 'verifyGlobalTwoFactor'])->name('verify');
    });


});

//password reset
Route::get('/password-reset', [AuthController::class, 'resetPasswordPage'])->name('reset.password.page');
Route::get('/password-reset-by-link', [AuthController::class, 'resetPasswordByLinkPage'])->name('reset.password.by.link.page');
Route::get('/password-reset-code-check', [AuthController::class, 'resetPasswordCodeCheck'])->name('reset.password.code.check');
Route::middleware(['throttle:password-reset'])->post('/password-reset-send-code', [AuthController::class, 'sendResetPasswordCode'])->name('reset.password.send.code');
Route::middleware(['throttle:password-reset'])->post('/password-reset', [AuthController::class, 'resetPassword'])->name('reset.password');

//verify email
Route::get('/verify-email', [AuthController::class, 'verifyEmailPage'])->name('user.verify.email.page');
Route::get('/verify-email-code-check', [AuthController::class, 'verifyEmailCodeCheck'])->name('user.verify.email.code.check');
Route::get('/verify-email-by-link', [AuthController::class, 'verifyEmailByLink'])->name('user.verify.email.by.link');
Route::middleware(['throttle:verify-email-phone'])->post('/verify-email', [AuthController::class, 'verifyEmail'])->name('user.verify.email');
Route::middleware(['throttle:verify-email-phone'])->post('/resend-verification-email', [AuthController::class, 'resendEmailVerification'])->name('user.send.verification.email');

//verify phone
Route::get('/verify-phone', [AuthController::class, 'verifyPhonePage'])->name('user.verify.phone.page');
Route::get('/verify-phone-code-check', [AuthController::class, 'verifyPhoneCodeCheck'])->name('user.verify.phone.code.check');
Route::middleware(['throttle:verify-email-phone'])->post('/verify-phone', [AuthController::class, 'verifyPhone'])->name('user.verify.phone');
Route::middleware(['throttle:verify-email-phone'])->post('/resend-verification-phone', [AuthController::class, 'resendPhoneVerification'])->name('user.send.verification.phone');
Route::middleware(['throttle:verify-email-phone'])->post('/send-verification-phone', [AuthController::class, 'sendPhoneVerificationCode'])->name('user.verification.phone.send');

//carts
// Route::get('/gift-cart', [GiftCartController::class, 'index'])->name('gift.cart');
// Route::post('/gift-cart/pay', [GiftCartController::class, 'pay'])->name('gift.cart.pay');

//Route::get('/pay/zarinpal-callback', [PayController::class, 'zarinpalCallback'])->name('gift.cart.callback.zarinpal');
//Route::post('/pay/behpardakht-callback', [PayController::class, 'behpardakhtCallback'])->withoutMiddleware(VerifyCsrfToken::class)->name('gift.cart.callback.behpardakht');
//Route::post('/pay/shahr-callback', [PayController::class, 'irankishCallback'])->withoutMiddleware(VerifyCsrfToken::class)->name('gift.cart.callback.irankish');

Route::post('/pay/verify', [PayController::class, 'verifyPayment'])->withoutMiddleware([VerifyCsrfToken::class]);

Route::get('/pay/verify', [PayController::class, 'verifyPayment'])->withoutMiddleware([VerifyCsrfToken::class]);

Route::get('/pay/cart/success', [PayController::class, 'purchaseSuccess'])->name('pay.success');
Route::get('/pay/coupon/success', [PayController::class, 'couponPurchaseSuccess'])->name('pay.coupon-success');

Route::prefix('/cart')->name('cart')->group(function () {
    Route::get('/', [CartController::class, 'index'])->name('.index');
    Route::post('/pay', [CartController::class, 'pay'])->name('.pay');
    Route::get('/pay', [CartController::class, 'payGet'])->name('.payget');
    Route::middleware(['throttle:add-to-cart'])->post('/add', [CartController::class, 'addToCart'])->withoutMiddleware(VerifyCsrfToken::class)->name('.add');
    Route::post('/remove', [CartController::class, 'removeFromCart'])->name('.remove');
    Route::post('/add/coupon', [CartController::class, 'addCoupon'])->name('.add.coupon');
    Route::post('/remove/coupon', [CartController::class, 'removeCoupon'])->name('.remove.coupon');
    Route::post('/add/gift-coupon', [CartController::class, 'addGiftCoupon'])->name('.add.gift.coupon');
    Route::get('/check-font-bought/{cartId}', [CartController::class, 'checkFontBought'])->name('.check-font-bought');
});

Route::post('payment-information', [UserPaymentInformationController::class, 'createOrUpdate'])->name('payment-information.modify');

Route::get('/tutorials/license-guide', function () {
    return view('front.pages.license-selection-guid.index');
});

Route::get('font-in-use', [PortfolioController::class, 'index'])->name('portfolios');
