<?php

namespace App\Http\Controllers\Front\Font;

use App\Classes\RR;
use App\Http\Controllers\Admin\Auth\PasswordHash;
use App\Http\Controllers\Controller;
use App\Http\Requests\Front\Font\Font\GenerateFontLabRequest;
use App\Models\Admin\Accounting\SalesData;
use App\Models\Admin\Comment\Comment;
use App\Models\Admin\Font\Associate;
use App\Models\Admin\Font\Category;
use App\Models\Admin\Font\Font;
use App\Models\Admin\Font\FontProblem;
use App\Models\Admin\Font\License;
use App\Models\Admin\Font\LicenseInfo;
use App\Models\Faq\Faq;
use App\Models\FontLab\LabFont;
use App\Models\InUse\InUse;
use App\Models\TagType;
use App\Models\User\User;
use App\Models\Utils\Tag;
use App\Traits\Front\Index\FilterFontTrait;
use Hek<PERSON>inasser\Verta\Facades\Verta;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Redis;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;
use JsonException;

/**
 *
 */
class FontController extends Controller
{
    use FilterFontTrait;

    /**
     * returns the font archive page
     * @param Request $request
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View
     */
    public function index(Request $request, $slug = null)
    {
        setCustomDBConfig();
        $title = 'All Fonts';
        $selectedCategory = new Collection();
        $categories = Category::where('show_in_search', 1)->orderBy('order_in_search', 'desc')->get();
        $fontTags = DB::table('tags as t')->join('taggables as tb', function ($join) {
            $join->on('t.id', '=', 'tb.tag_id');
            $join->on('tb.taggable_type', '=', DB::raw("'App\\\Models\\\Admin\\\Font\\\Font'"));
        })
            ->where('show_in_search', 1)
            ->select('t.id', 't.name', 'type', 't.slug')
            ->groupBy('t.id')
            ->orderBy('order_in_search', 'desc')->get();
        if (isset($slug)) {
            $category = Category::where('slug', $slug)->first();
            if (isset($category)) {
                $selectedCategory = $category;
                $title = $category->name;
            }
        }
        if ($selectedCategory->count() <= 0) {
            $selectedCategory->id = 'all';
        }
        if ($request->type) {
            $selectedCategory->id = $request->type;
        }
        $tagTypes = \App\Models\TagType::with('tags')
            ->whereHas('tags', function ($query) {
                $query->where('show_in_filter', true);
            })
            ->orderByDesc('priority')
            ->get();
        return view('front.pages.font.index', compact(['tagTypes', 'categories', 'fontTags', 'slug', 'title', 'selectedCategory']));
    }

    /**
     * returns the single font page
     * @param Request $request
     * @return \Illuminate\Contracts\Foundation\Application|\Illuminate\Contracts\View\Factory|\Illuminate\Contracts\View\View|never
     */
    public function single(Request $request)
    {
        $slug = sanitizeInput($request->slug);
        if (!isset($slug))
            return abort(404);

        $font = Font::where('slug', $slug)->ShouldShow()->first();
        if (!isset($font))
            return abort(404);
        $owners = getFontOwners($font->id);

        $similarFonts = $this->getFilteredFont($font->categories->pluck('id')->take(3)->toArray(), 7)['fonts'];
        $packages = $font->packages()->where('status', 'active')->orderBy(DB::raw('cast(packages.price as int)'), 'DESC')->get();
        // $licenseDetails = $font->licenseDetails()->where('status', 'active')->whereHas('license', function ($query) {
        //     $query->orderBy('priority', 'desc');
        // })->get();
        $licenseDetails = $font->licenseDetails()
            ->select('font_license_details.*')
            ->join('licenses', 'font_license_details.license_id', '=', 'licenses.id')
            ->where('font_license_details.status', 'active')
            ->where('licenses.status', 'active')
            ->orderBy('licenses.priority', 'desc')
            ->get();
        $licenseInfos = LicenseInfo::all();
        $licenses = License::where('status', 'active')->orderByDesc('priority')->get();
        $similarFonts = $similarFonts->filter(fn($obj) => $obj->id !== $font->id);

        $faqs = Faq::where('show_in_font_page', 1)->orderByDesc('priority')->get();
        $associates = User::join('user_designers as ud', 'ud.user_id', '=', 'users.id')
            ->join('user_infos as ui', 'ui.user_id', 'users.id')
            ->join('font_associates as fa', 'fa.user_id', '=', 'ud.user_id')
            ->join('fonts as f', [['f.id', '=', 'fa.font_id'], ['f.id', '=', DB::raw($font->id)]])
            ->where('ud.deleted_at', null)
            ->where('ui.deleted_at', null)
            ->where('fa.deleted_at', null)
            ->where('f.deleted_at', null)
            ->select('users.id as id', 'ud.user_id as designer_id', 'users.avatar as user_avatar', 'ud.cover_sm', 'ud.cover_lg', 'ud.profile_picture'
                , 'ud.designer_slug', 'users.name', 'fa.font_id', 'fa.role', 'ui.biography')
            ->orderBy('fa.created_at', 'asc')->where([['fa.show', 'active'], ['f.id', $font->id], ['fa.deleted_at', '=', null]])
            ->get()->unique('id');


        $inuses = $font->inUses()->with(['firstDesigner', 'secondDesigner', 'thirdDesigner', 'fonts', 'font', 'categories'])->get();
        $inuses = $inuses->sortByDesc('priority');

        return view('front.pages.font.single', compact(['inuses', 'owners', 'licenses', 'licenseInfos', 'associates', 'faqs', 'font', 'packages', 'licenseDetails', 'similarFonts']));
    }

    /**
     * returns the comments of the font based on given limit and offset
     * @param Request $request
     * @return array|never
     */
    public function getComments(Request $request)
    {
        $slug = $request->slug;
        if (!isset($slug))
            return abort(404);
        $limit = $request->limit ?? 7;
        $offset = $request->offset ?? 0;
        $comments = Redis::command('zrevrange', ['font_set_comments:' . $slug, 0, -1]);
        $count = count($comments);
        if ($count <= 0) {
            $keys = Redis::command('keys', ['font_set_comments:' . $slug]);
            if (count($keys) <= 0) {
                RR::applyRefreshComments($slug);
                $comments = Redis::command('zrevrange', ['font_set_comments:' . $slug, 0, -1]);
                $count = count($comments);
            }
        }
        $comments = collect($comments);
        $comments = $comments->skip($offset * $limit)->take($limit);

        return [
            'comments' => $comments,
            'count' => $count,
        ];
    }


    /**
     * sends a request to font lab then returns the images that lab returns
     */
    public function generateFontLabImages(GenerateFontLabRequest $request)
    {
        // if (!verifyRecaptcha($request)) {
        //     return abort(429);
        // }
        $fontId = $request->font_id;

        $labFont = DB::connection('mysql_fa')->table('lab_fonts')->where('english_font_id', $fontId)->first();
        if ($labFont == null)
            return [];
        $labId = $labFont?->id;
        $slug = $labFont?->slug;
        $text = $request->text != '' ? "&text=" . sanitizeInput($request->text) : '';
        $toReturn = [];
        if ($request->isFirst == 'true') {
            $url = config('app.font_lab_url') . "/defImages/{$slug}/englishPageImages.json";


            $response = Http::timeout(20)->get($url);
            try {
                $toReturn[] = json_decode($response->getBody(), false, 512, JSON_THROW_ON_ERROR);
            } catch (JsonException $e) {
            }
            $childSlugs = DB::connection('mysql_fa')->table('lab_fonts')->where('parent_id', $labId)->get()->pluck('slug');
            foreach ($childSlugs as $childSlug) {
                $url = config('app.font_lab_url') . "/defImages/{$childSlug}/englishPageImages.json";
                $response = Http::timeout(20)->get($url);
                if ($response->status() == 200) {
                    try {
                        $toReturn[] = json_decode($response->getBody(), false, 512, JSON_THROW_ON_ERROR);
                    } catch (JsonException $e) {
                    }
                }
            }
        } elseif (isset($labId)) {
            $response = Http::timeout(20)->get(config('app.font_lab_url') . "/generateEnglishImages?id=" . $labId . $text);
            try {
                if (isset($response)) {
                    $toReturn[] = json_decode($response->getBody(), true, 512, JSON_THROW_ON_ERROR);
                }
            } catch (JsonException $exception) {
            }
        }

        return $toReturn;
    }


    /**
     * this method returns a list of fonts based on given category ids ,
     * if value 'all' exists in request with key categories it returns
     * top 9 fonts based on priority_date
     * @throws \JsonException
     */
    public function filterFonts(Request $request)
    {
        $request->validate([
            'tags' => 'nullable|array',
            'tags.*' => [
                'nullable',
                'string',
                'exists:tags,slug'
            ],
            'categories' => 'nullable|array',
            'categories.*' => [
                'nullable',
                'string',
                'exists:categories,slug'
            ],
            'limit' => 'nullable|integer|min:1',
            'offset' => 'nullable|integer|min:0',
            'isFirst' => 'nullable|boolean',
            'sort' => 'nullable|string|in:newest,price,popular',
        ]);

        $tags = $request->tags;
        $categories = $request->categories;
        $tags = Tag::whereIn('slug', $tags)->pluck('id')->toArray();
        $categories = Category::whereIn('slug', $categories)->pluck('id')->toArray();
        $limit = sanitizeInput($request->limit) ?? 12;
        $offset = sanitizeInput($request->offset) ?? 0;
        $isFirst = $request->isFirst == 'true';
        $sort = sanitizeInput($request->sort);
        $isAll = count($tags) == 0 && count($categories) == 0;
        if ($isFirst)
            $filtered = $this->getLatestFonts($limit, $offset, true);
        else
            $filtered = $this->fetchFontsFromRedis($categories, $tags, $isAll, $sort, $limit, $offset);

        $filtered['boughtFonts'] = getUserBoughtFontsId();
        return json_encode($filtered);
    }

    private function fetchFontsFromRedis($categories, $fontTags, $isAll = false, $sort = "newest", $limit = 12, $offset = 0)
    {
        setCustomDBConfig();
        // Combine the category types and tag IDs to generate a unique Redis key
        $redisKey = 'filter_fonts:' . implode(',', $categories) . ':' . implode(',', $fontTags) . ':' . $sort;

        // Check if the fonts are already cached in Redis
        /*$cachedFonts = Redis::get($redisKey);
        if ($cachedFonts !== null) {
            return json_decode($cachedFonts, true);
        }*/
        $query = Font::selectRaw(' fonts.id, fonts.name, fonts.slug, fonts.thumbnail_url, fonts.prime_price, fonts.priority_date, fi.weights_count, fonts.main_color, fonts.banner_url')
            ->leftJoin('font_infos as fi', 'fi.font_id', '=', 'fonts.id')
            ->where('fonts.status', '=', 'published')
            ->whereNull('fonts.deleted_at')
            ->orderByDesc('fonts.priority_date')
            ->groupBy('id')->disableCache();

        if (!$isAll && (!empty($categories) || !empty($fontTags))) {

            if (!empty($categories)) {
                $query->whereHas('categories', function ($q) use ($categories) {
                    $q->whereIn('categories.id', $categories)
                        ->whereNull('categories.deleted_at');
                }, '=', count($categories));
            }

            if (!empty($fontTags)) {
                $query->whereHas('tags', function ($q) use ($fontTags) {
                    $q->whereIn('id', $fontTags)
                        ->whereNull('tags.deleted_at');
                }, '=', count($fontTags));
            }

        }

        $fonts = $query->get();

        $cats = DB::table('font_category as fc')->join(
            'categories as c', function ($join) use ($fonts) {
            $join->on('c.id', '=', 'fc.category_id')
                ->whereIn("fc.font_id", $fonts->pluck('id')->toArray());
        })->whereNull('c.deleted_at')->select('c.type', 'c.id', 'c.name', 'c.slug', 'fc.font_id')->get();


        $count = $fonts->count();
        $fonts = $fonts
            ->when($sort == 'newest', function ($query) {
                return $query->sortByDesc('priority_date');
            })->when($sort == 'price', function ($query) {
                return $query->sortByDesc('prime_price');
            })->when($sort == 'popular', function ($query) use ($fontTags) {
                $pop = getPopularSalesData()->pluck('sum_package_count', 'font_id');
                $query->each(function ($font) use ($pop) {
                    $font->sales_count = intval($pop[$font->id] ?? 0);
                });
                return $query->sortByDesc('sales_count');
            })->flatten()
            ->skip($offset * $limit)->take($limit);
        foreach ($fonts as $font) {
            $topThree = $cats->where('font_id', $font->id)->take(3);
            $font->topCategories = $topThree->toArray();
        }

        Redis::set($redisKey, json_encode($fonts));
        return [
            'fonts' => $fonts,
            'count' => $count,
        ];
    }

    private function getFilterFonts($categories = 'all', $limit = 12, $offset = 0, $tags = 'all', $sort = "newest")
    {
        setCustomDBConfig();
        if (is_array($categories))
            $categories = collect($categories)->map(function ($category) {
                return htmlspecialchars(strip_tags($category));
            })->toArray();
        else
            $categories = 'all';

        if (is_array($tags))
            $tags = collect($tags)->map(function ($tag) {
                return htmlspecialchars(strip_tags($tag));
            })->toArray();
        else
            $tags = 'all';


        $fonts = DB::table('fonts as f')->join('font_infos as fi', 'fi.font_id', '=', 'f.id');
        if ($tags != 'all') {
            $fonts = $fonts->join('taggables as t', function ($join) use ($tags) {
                $join->on('t.taggable_id', '=', 'f.id')
                    ->where('t.taggable_type', '=', Font::class)
                    ->whereIn('t.tag_id', $tags);
            });
        }
        $hasCategory = isset($categories) && count($categories) > 0 && $categories != 'all';
        if ($hasCategory) {
            $fonts = $fonts->join('font_category as fc', 'fc.font_id', 'f.id')
                ->join('categories as c', function ($join) {
                    $join->on('c.id', '=', 'fc.category_id');
                });

            $fonts = $fonts->whereIn('c.id', $categories);
            $fonts = $fonts->whereNull('c.deleted_at');
        }

        $fonts = $fonts->where('f.status', '=', 'published')
            ->whereNull('f.deleted_at')
            ->select('f.id', 'f.name', 'f.slug', 'f.thumbnail_url', 'f.prime_price', 'f.priority_date', 'fi.weights_count', 'f.main_color', 'f.banner_url')
            ->orderByDesc('f.priority_date')
            ->groupBy('f.id')
            ->get();


        $cats = DB::table('font_category as fc')->join(
            'categories as c', function ($join) use ($fonts) {
            $join->on('c.id', '=', 'fc.category_id')
                ->whereIn("fc.font_id", $fonts->pluck('id')->toArray());
        })->whereNull('c.deleted_at')->select('c.type', 'c.id', 'c.name', 'c.slug', 'fc.font_id')->get();
        $count = $fonts->count();
        $fonts = $fonts->sortByDesc('priority_date')->skip($offset * $limit)->take($limit);
        foreach ($fonts as $font) {
            $topThree = $cats->where('font_id', $font->id)->take(3);
            $font->topCategories = $topThree->toArray();
        }
        return [
            'fonts' => $fonts,
            'count' => $count,
        ];
    }


    /**
     * @param Request $request (slug)
     * @return never|void
     * return single font update by slug of font_problems
     */
    public function getFontUpdate(Request $request)
    {

        $slug = $request->slug;
        if (!isset($slug))
            return abort(404);
        $font_update = FontProblem::where('type', 'update')->where('slug', $slug)->first();
        if (!isset($font_update) || !isset($slug))
            return abort(404);
        return view('front.pages.font.show-update', compact('font_update'));

    }

}
