<?php

namespace App\Http\Controllers\Admin\Carts;

use App\Http\Controllers\Admin\Accounting\UpdateSalesData;
use App\Http\Controllers\Controller;
use App\Models\Admin\Font\Associate;
use App\Models\Carts\CartLicense;
use App\Models\Carts\CartPackage;
use App\Models\Carts\Pay;
use App\Services\PaymentService;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class PayController extends Controller
{
    /**
     * a list of pays, the data is coming from pays api in DataApiController
     *
     * @return View|\never
     */
    public function index()
    {
        //on lack of permission
        if (!auth()->user()->can('can-view-pay')) return abort(403);

        return view('admin.pay.index');
    }

    /**
     * a list of designer earns in the last 30 days
     *
     * @return View|\never
     */
    public function designerPays()
    {
        //on lack of permission
        if (!auth()->user()->can('can-view-designer-pay')) return abort(403);

        $tomorrow = now()->addDay();
        $lastMonth = now()->subDays(3000);

        //font association ids
        $associateData = Associate::where('user_id', Auth::user()->id)
            ->get();

        $sales = array();

        foreach ($associateData as $associate) {

            //setting date end
            if ($associate->date_end == null) $associate->date_end = now()->addDay()->format('Y-m-d');
            else $associate->date_end = Carbon::parse($associate->date_end)->addDay();

            $cartPackages = CartPackage::join('carts', 'cart_packages.cart_id', '=', 'carts.id')
                ->join('fonts', 'cart_packages.font_id', '=', 'fonts.id')
               
                ->where('cart_packages.font_id', $associate->font_id)
                ->where('carts.status', 'success')
                ->where('carts.updated_at', '>=', $lastMonth->format('Y-m-d'))
                ->where('carts.updated_at', '<', $tomorrow->format('Y-m-d'))
                ->where('carts.updated_at', '>=', $associate->date_start)
                ->where('carts.updated_at', '<', $associate->date_end)
                ->where('fonts.deleted_at', null)
                ->where('cart_packages.package_price', '>',0)
                ->where('cart_packages.original_package_price', '>',0)
                // ->where(function ($query) {

                //     $query->orWhere(function ($query2) {
                //         $query2->where('cart_packages.package_price', '>', 0)
                //                 ->where('cart_packages.original_package_price', '>', 0);
                //     })->orWhere(function ($query2) {
                //         $query2->where('cart_packages.package_price', '=', 0)
                //                 ->where('cart_packages.original_package_price', '=', 0);
                //     });
                // })
                ->select(
                    DB::raw('cart_packages.package_price - cart_packages.affiliate_earn as amount'),
                    'fonts.name',
                    'carts.updated_at',
                    'fonts.thumbnail_url',
                )
                ->get();


            $cartLicenses = CartLicense::join('carts', 'cart_licenses.cart_id', '=', 'carts.id')
                ->join('fonts', 'cart_licenses.font_id', '=', 'fonts.id')
                ->join('font_license_details', 'cart_licenses.font_license_details_id', '=', 'font_license_details.id')
                ->join('licenses', 'font_license_details.license_id', '=', 'licenses.id')
              
                ->where('cart_licenses.font_id', $associate->font_id)
                ->where('carts.status', 'success')
                ->where('carts.updated_at', '>=', $lastMonth->format('Y-m-d'))
                ->where('carts.updated_at', '<', $tomorrow->format('Y-m-d'))
                ->where('carts.updated_at', '>=', $associate->date_start)
                ->where('carts.updated_at', '<', $associate->date_end)
                ->where('fonts.deleted_at', null)
                ->where('font_license_details.deleted_at', null)
                ->where('licenses.deleted_at', null)
                ->where('cart_licenses.license_price', '>',0)
                ->where('cart_licenses.original_license_price', '>',0)
                // ->where(function ($query) {

                //     $query->orWhere(function ($query2) {
                //         $query2->where('cart_licenses.license_price', '>', 0)
                //                 ->where('cart_licenses.original_license_price', '>', 0);
                //     })->orWhere(function ($query2) {
                //         $query2->where('cart_licenses.license_price', '=', 0)
                //                 ->where('cart_licenses.original_license_price', '=', 0);
                //     });
                // })
                ->select(
                    DB::raw('cart_licenses.license_price as amount'),
                    'fonts.name',
                    'carts.updated_at',
                    DB::raw('licenses.name as license_name'),
                    'licenses.thumbnail_url'
                )
                ->get();

            foreach ($cartPackages as $data) {
                //amount in association percentage
                $data->amount = (($data->amount / 100) * $associate->percent);
                //adding percentage
                $data->percentage = $associate->percent;
                array_push($sales, $data);
            }

            foreach ($cartLicenses as $data) {
                //amount in association percentage
                $data->amount = (($data->amount / 100) * $associate->percent);
                //custom name
                $data->name = 'لایسنس ' . $data->license_name . ' فونت ' . $data->name;
                //adding percentage
                $data->percentage = $associate->percent;
                array_push($sales, $data);
            }
        }

        $sales = collect($sales)->sortByDesc('updated_at');

        return view('admin.pay.designer', compact('sales'));
    }

    public function verifyYekpay(Pay $pay,Request $request,PaymentService $paymentService) {
        $cart = $pay->payable;
        $gateway="yekpay";
        $callBackUpdate = $paymentService->verifyPayment($gateway, 1, $pay->token);
        $paymentService->purchaseSuccess($pay, $cart, $request, $callBackUpdate);
        return 'success';

    }
    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Http\Response
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @return \Illuminate\Http\Response
     */
    public function store(Request $request)
    {
        //
    }

    /**
     * Display the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function show($id)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function edit($id)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     *
     * @param \Illuminate\Http\Request $request
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function update(Request $request, $id)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param int $id
     * @return \Illuminate\Http\Response
     */
    public function destroy($id)
    {
        //
    }
}
