<?php

namespace App\Http\Controllers\Admin\Accounting;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\SearchDesignerFontSalesReportRequest;
use App\Http\Requests\Admin\SearchDesignerFontsSalesReportRequest;
use App\Http\Requests\Admin\SearchDesignerSalesReportRequest;
use App\Http\Requests\Admin\SearchFontSalesReportRequest;
use App\Models\Admin\Accounting\SalesData;
use App\Models\User\DesignerEarn;
use App\Models\Admin\Font\Associate;
use App\Models\Admin\Font\Font;
use App\Models\Carts\Cart;
use App\Models\Carts\CartLicense;
use App\Models\Carts\CartPackage;
use App\Models\User\User;
use Carbon\Carbon;
use Exception;
use Hekmatinasser\Verta\Facades\Verta;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;

class SalesReportController extends Controller
{

    /**
     * Display statistics and outcome of a font for an admin user
     *
     * @param App\Models\Admin\Font\Font $font
     * @param App\Http\Requests\Admin\SearchFontSalesReportRequest $request
     * @return View|\never
     */
    public function fontSalesReport(Font $font, SearchFontSalesReportRequest $request)
    {
        setCustomDBConfig();
        //on lack of permission
        if (!auth()->user()->can('can-view-font-sales-report')) return abort(403);

        //============================for search
        $dateStart = $request->date_start;
        $dateEnd = $request->date_end;

        $dateStartGregorian = null;
        $dateEndGregorian = null;

        if ($request->date_start != null) $dateStartGregorian = getGregorian($request->date_start);
        if ($request->date_end != null) $dateEndGregorian = Carbon::parse(getGregorian($request->date_end))->addDay()->format('Y-m-d');
        //=======================================

        //============================declaring dates
        //now in shamsi
        $now = verta();
        $now->day = 1;

        //last month in shamsi
        $lastMonth = $now->subMonth(1);

        //first day of last shamsi month in gregorian
        $firstDayOfLastMonth = getGregorian(Verta::createJalali(verta()->day(1)->subMonth()->year, verta()->day(1)->subMonth()->month, 1));

        //last day of last shamsi month in gregorian
        $lastDayOfLastMonth = getGregorian(Verta::createJalali(verta()->day(1)->subMonth()->year, verta()->day(1)->subMonth()->month, verta()->day(1)->subMonth()->daysInMonth));

        //now in gregorian
        $gregorianNow = now()->format('Y-m-d');

        //first day of this shamsi month in gregorian
        $firstDayOfThisMonth = getGregorian(Verta::createJalali(verta()->day(1)->year, verta()->day(1)->month, 1));
        //==========================================

        $data = SalesData::where('font_id', $font->id);

        // ==================================applying date search
        if ($dateStartGregorian != null) {
            $data = $data->where('date', '>=', $dateStartGregorian);
        }

        if ($dateEndGregorian != null) {
            $data = $data->where('date', '<', $dateEndGregorian);
        }
        //========================================================

        $data = $data->select(
            DB::raw('SUM(package_income) as fontDiscountLessIncome'),
            DB::raw('SUM(package_pure_income - affiliate_earn) as fontIncome'),
            DB::raw('SUM(package_count) as fontCount'),
            DB::raw('SUM(license_income) as licenseDiscountLessIncome'),
            DB::raw('SUM(license_pure_income) as licenseIncome'),
            DB::raw('SUM(license_count) as licenseCount'),
            DB::raw('SUM(package_discount) as fontDiscount'),
            DB::raw('SUM(license_discount) as licenseDiscount'),
            DB::raw('SUM(package_coupon_discount) as fontCouponDiscount'),
            DB::raw('SUM(license_coupon_discount) as licenseCouponDiscount'),
            DB::raw('SUM(package_group_discount) as fontGroupDiscount'),
            DB::raw('SUM(license_group_discount) as licenseGroupDiscount'),
            DB::raw('SUM(previous_package_discount) as previousPackageDiscount'),
            DB::raw('SUM(previous_license_discount) as previousLicenseDiscount'),
            DB::raw('SUM(affiliate_earn) as affiliateEarn'),
            DB::raw('SUM(affiliate_coupon_total) as affiliateDiscount')
        )
            ->get()[0];

        $totalThisMonth = SalesData::where('font_id', $font->id)
            ->where('date', '>=', $firstDayOfThisMonth)
            ->select(
                DB::raw('SUM(package_pure_income - affiliate_earn + license_pure_income) as amount'),
            )
            ->get()[0];

        $totalLastMonth = SalesData::where('font_id', $font->id)
            ->where('date', '>=', $firstDayOfLastMonth)
            ->where('date', '<', $firstDayOfThisMonth)
            ->select(
                DB::raw('SUM(package_pure_income - affiliate_earn + license_pure_income) as amount'),
            )
            ->get()[0];

        //=======================================getting sales data

        //====================================income
        $data->totalIncome = $data->fontIncome + $data->licenseIncome;
        //==========================================

        //=====================================total income of this month
        $data->totalIncomeThisMonth = (int)$totalThisMonth->amount;
        //===============================================================

        //=====================================total income of last month
        $data->totalIncomeLastMonth = (int)$totalLastMonth->amount;
        //===============================================================

        //======================================income without discount
        $data->totalDiscountLessIncome = $data->fontDiscountLessIncome + $data->licenseDiscountLessIncome;
        //=============================================================

        //=========================================================

        //====================================associations values
        $associations = Associate::leftJoin('sales_data', function ($join) {
            $join->on('sales_data.font_id', 'font_associates.font_id');
            $join->on('sales_data.date', '>=', 'font_associates.date_start');
            $join->on(DB::raw('( sales_data.date <= font_associates.date_end OR font_associates.date_end IS NULL )'), 'IS', DB::raw('TRUE'));
        })->where('font_associates.font_id', $font->id);


        // ==================================applying date search
        if ($dateStartGregorian != null) {
            $associations = $associations->where('sales_data.date', '>=', $dateStartGregorian);
        }

        if ($dateEndGregorian != null) {
            $associations = $associations->where('sales_data.date', '<', $dateEndGregorian);
        }
        //========================================================

        $associations = $associations->select(
            'font_associates.id',
            'font_associates.user_id',
            'font_associates.font_id',
            'font_associates.role',
            'font_associates.percent',
            'font_associates.date_start',
            'font_associates.date_end',
            'font_associates.status',
            'font_associates.show',
            DB::raw("Sum((sales_data.package_pure_income / 100) * font_associates.percent)
                - Sum((sales_data.affiliate_earn / 100 ) * font_associates.percent) as fontIncome"),
            DB::raw("Sum((sales_data.license_pure_income / 100) * font_associates.percent) as licenseIncome"),
            DB::raw("Sum((sales_data.affiliate_earn / 100) * font_associates.percent) as affiliateEarn")
        )->groupBy(
            'font_associates.id',
        )->get();


        foreach ($associations as $association) {
            //total income
            $association->value = $association->fontIncome + $association->licenseIncome;

            //==========================last month income
            $association->lastMonthIncome = (int)DB::select("
                SELECT Sum((sales_data_last_month.package_pure_income / 100) * font_associates.percent)
                    + Sum((sales_data_last_month.license_pure_income / 100) * font_associates.percent)
                    - Sum((sales_data_last_month.affiliate_earn / 100 ) * font_associates.percent) as lastMonthIncome
                FROM   font_associates
                LEFT JOIN sales_data as sales_data_last_month
                        ON sales_data_last_month.font_id = font_associates.font_id
                            AND sales_data_last_month.date >= font_associates.date_start
                            AND sales_data_last_month.date >= '" . $firstDayOfLastMonth . "'
                            AND sales_data_last_month.date <= '" . $lastDayOfLastMonth . "'
                            AND ( sales_data_last_month.date <= font_associates.date_end
                                    OR font_associates.date_end IS NULL )
                WHERE  font_associates.id = " . $association->id . "
                AND font_associates.deleted_at IS NULL
                GROUP BY font_associates.id;
            ")[0]->lastMonthIncome;
            //==========================================

        }
        //=======================================================

        //=======================================package outcome
        $packages = $font->packages;
        $pData = array();
        foreach ($packages as $package) {
            $package->outcome = (int)DB::select("SELECT SUM(package_price - affiliate_earn) as value FROM cart_packages WHERE package_id = " . $package->id)[0]->value;

            array_push($pData, ['value' => $package->outcome, 'category' => $package->title]);
        }
        $pData = collect($pData)->sortByDesc('value');

        //sorting by value
        $packageData = array();
        foreach ($pData as $package) {
            array_push($packageData, ['value' => $package['value'], 'category' => $package['category']]);
        }
        //======================================================

        return view('admin.accounting.sales-report.font.admin.font', compact('font', 'data', 'packageData', 'associations', 'dateStart', 'dateEnd'));
    }

    /**
     * Display statistics and outcome of designers
     *
     * @param App\Http\Requests\Admin\SearchDesignerSalesReportRequest $request
     * @return View|\never
     */
    public function designersSalesReport(SearchDesignerSalesReportRequest $request)
    {
        setCustomDBConfig();
        //on lack of permission
        if (!auth()->user()->can('can-view-designer-sales-report')) return abort(403);
        //============================for search
        $dateStart = $request->date_start;
        $dateEnd = $request->date_end;

        $dateStart = $request->dateStart;
        $dateEnd = $request->dateEnd;

        $dateStartGregorian = now()->subYears(100);
        $dateEndGregorian = now();

        if ($dateStart != null) $dateStartGregorian = Carbon::parse(getGregorian($dateStart));
        if ($dateEnd != null) $dateEndGregorian = Carbon::parse(getGregorian($dateEnd))->addDay();
        //=======================================

        $designers = User::join('font_associates as fa', 'fa.user_id', '=', 'users.id')->orderBy('users.id')
            ->select('users.id', 'users.name', 'users.email', 'users.name_bank')
            ->groupBy('users.id')
            ->get();

        //now in shamsi

        //last month in shamsi

        //first day of this shamsi month in gregorian
        $firstDayOfThisMonth = getGregorian(Verta::createJalali(verta()->day(1)->year, verta()->day(1)->month, 1));

        //first day of last shamsi month in gregorian
        $firstDayOfLastMonth = getGregorian(Verta::createJalali(verta()->day(1)->subMonth(1)->year, verta()->day(1)->subMonth(1)->month, 1));
        //last day of last shamsi month in gregorian
        $lastDayOfLastMonth = getGregorian(Verta::createJalali(verta()->day(1)->subMonth(1)->year, verta()->day(1)->subMonth(1)->month, verta()->day(1)->subMonth(1)->daysInMonth));

        if (($dateStart != null) || ($dateEnd != null)) {
            $total = DB::select(
                "SELECT
                    font_associates.user_id,
                    Sum(((sales_data.package_pure_income + sales_data.license_pure_income - sales_data.affiliate_earn) / 100) * font_associates.percent) as total,
                    SUM(package_count + license_count) as totalCount
                FROM
                    font_associates
                    LEFT JOIN sales_data ON sales_data.font_id = font_associates.font_id

                WHERE font_associates.deleted_at IS NULL
                 AND sales_data.date >= font_associates.date_start
                    AND (
                    sales_data.date <= font_associates.date_end
                    OR font_associates.date_end IS NULL
                    )
                    AND sales_data.date >= '{$dateStartGregorian}'
                    AND sales_data.date < '{$dateEndGregorian}'
                GROUP BY
                    font_associates.user_id
                ORDER BY
                    font_associates.user_id
                "
            );
            $totalUnpaid = DB::select(
                "SELECT
                    font_associates.user_id,
                    Sum(((sales_data.package_pure_income + sales_data.license_pure_income - sales_data.affiliate_earn) / 100) * font_associates.percent) as total
                FROM
                    font_associates
                    LEFT JOIN sales_data ON sales_data.font_id = font_associates.font_id

                WHERE font_associates.deleted_at IS NULL
                AND sales_data.date >= font_associates.date_start
                    AND (
                    sales_data.date <= font_associates.date_end
                    OR font_associates.date_end IS NULL
                    )
                    AND sales_data.designer_paid = 0
                    AND sales_data.date >= '{$dateStartGregorian}'
                    AND sales_data.date < '{$dateEndGregorian}'
                GROUP BY
                    font_associates.user_id
                ORDER BY
                    font_associates.user_id
                ;"
            );
            $totalMap = collect($total)->keyBy('user_id');
            $totalUnpaidMap = collect($totalUnpaid)->keyBy('user_id');


            foreach ($designers as $key => $designer) {
                $userId = $designer->id; // Assuming 'id' is the user_id in designers
                $designer->total = $totalMap[$userId]->total ?? 0;
                $designer->totalUnpaid = $totalUnpaidMap[$userId]->total ?? 0;
                $designer->salesCount = $totalMap[$userId]->totalCount ?? 0;

            }


            $withOutFontiranAssociate = DB::table('fonts')
                ->leftJoin('font_associates', function ($join) use ($firstDayOfThisMonth) {
                    $join->on('fonts.id', '=', 'font_associates.font_id')
                        ->where('font_associates.role', '=', 'font_iran')
                        ->where('font_associates.status', '=', 'active')
                        ->whereNull('font_associates.deleted_at')
                        ->where(function ($query) use ($firstDayOfThisMonth) {
                            $query->whereNull('date_end')
                                ->orWhere('date_end', '>', $firstDayOfThisMonth);
                        });
                })
                ->whereNull('fonts.deleted_at')
                ->whereNull('font_associates.id')
                ->select('fonts.id')
                ->get()->pluck('id')->toArray();

            $adminsTotalUnpaid = SalesData::select(
                DB::raw('
                (Sum((sales_data.package_pure_income + sales_data.license_pure_income - sales_data.affiliate_earn))) as totalUnpaid
                '))
                ->where('designer_paid', '=', 0)
                ->where('date', '>=', $dateStartGregorian)
                ->where('date', '<', $dateEndGregorian)
                ->whereNotIn('font_id', $withOutFontiranAssociate)
                ->first();

            $adminsTotal = SalesData::select(
                DB::raw('(SUM((package_pure_income - affiliate_earn + license_pure_income) )) as total'))
                ->whereNotIn('font_id', $withOutFontiranAssociate)
                ->where('date', '>=', $dateStartGregorian)
                ->where('date', '<', $dateEndGregorian)
                ->first();

            $adminsThisMonth = SalesData::select(
                DB::raw('(Sum((sales_data.package_pure_income + sales_data.license_pure_income - sales_data.affiliate_earn) )) as totalThisMonth'))
                ->where('date', '>=', $dateStartGregorian)
                ->where('date', '<', $dateEndGregorian)
                ->whereNotIn('font_id', $withOutFontiranAssociate)
                ->first();

            $techAdmin = new User();
            $techAdmin->id = config('fontiran.techAdmin.id');
            $techAdmin->name = config('fontiran.techAdmin.name');
            $techAdmin->email = config('fontiran.techAdmin.email');
            $techAdmin->info = (object)config('fontiran.techAdmin.info');
            $tPercent = floatval(config('fontiran.techAdmin.percent')) / 100;
            $techAdmin->total = ($adminsTotal->total * $tPercent);
            $techAdmin->totalUnpaid = ($adminsTotalUnpaid->totalUnpaid * $tPercent);
            $techAdmin->totalThisMonth = ($adminsThisMonth->totalThisMonth * $tPercent);

            $productAdmin = new User();
            $productAdmin->id = config('fontiran.productAdmin.id');
            $productAdmin->name = config('fontiran.productAdmin.name');
            $productAdmin->email = config('fontiran.productAdmin.email');
            $productAdmin->info = (object)config('fontiran.productAdmin.info');
            $pPercent = floatval(config('fontiran.productAdmin.percent')) / 100;
            $productAdmin->total = ($adminsTotal->total * $pPercent);
            $productAdmin->totalUnpaid = ($adminsTotalUnpaid->totalUnpaid * $pPercent);
            $productAdmin->totalThisMonth = ($adminsThisMonth->totalThisMonth * $pPercent);

            $support = new User();
            $support->id = config('fontiran.support.id');
            $support->name = config('fontiran.support.name');
            $support->email = config('fontiran.support.email');
            $support->info = (object)config('fontiran.support.info');
            $sPercent = floatval(config('fontiran.support.percent')) / 100;
            $support->total = ($adminsTotal->total * $sPercent);
            $support->totalUnpaid = ($adminsTotalUnpaid->totalUnpaid * $sPercent);
            $support->totalThisMonth = ($adminsThisMonth->totalThisMonth * $sPercent);


            $legal = new User();
            $legal->id = config('fontiran.legal.id');
            $legal->name = config('fontiran.legal.name');
            $legal->email = config('fontiran.legal.email');
            $legal->info = (object)config('fontiran.legal.info');
            $lPercent = floatval(config('fontiran.legal.percent')) / 100;
            $legal->total = ($adminsTotal->total * $lPercent);
            $legal->totalUnpaid = ($adminsTotalUnpaid->totalUnpaid * $lPercent);
            $legal->totalThisMonth = ($adminsThisMonth->totalThisMonth * $lPercent);


            $fontiran = $designers->where('id', 1)->first();
            $totalToMinus = $productAdmin->total + $techAdmin->total + $support->total + $legal->total;
            $totalUnpaidToMinus = $productAdmin->totalUnpaid + $techAdmin->totalUnpaid + $support->totalUnpaid + $legal->totalUnpaid;
            $totalLastMonthToMinus = $productAdmin->totalLastMonth + $techAdmin->totalLastMonth + $support->totalLastMonth + $legal->totalLastMonth;
            $totalThisMonthToMinus = $productAdmin->totalThisMonth + $techAdmin->totalThisMonth + $support->totalThisMonth + $legal->totalThisMonth;
            $fontiran->total -= $totalToMinus;
            $fontiran->totalUnpaid -= $totalUnpaidToMinus;
            $fontiran->totalLastMonth -= $totalLastMonthToMinus;
            $fontiran->totalThisMonth -= $totalThisMonthToMinus;

            $designers = $designers->sortByDesc('total');
            $designers->splice(1, 0,
                [$techAdmin, $productAdmin, $support, $legal]
            );
        } else {
            $total = DB::select(
                "SELECT
                    font_associates.user_id,
                    SUM(((package_pure_income - affiliate_earn + license_pure_income) / 100) * font_associates.percent) as total,
                    SUM(package_count + license_count) as totalCount
                FROM
                    font_associates
                    LEFT JOIN sales_data ON sales_data.font_id = font_associates.font_id

                WHERE font_associates.deleted_at IS NULL
                AND sales_data.date >= font_associates.date_start
                    AND (
                    sales_data.date <= font_associates.date_end
                    OR font_associates.date_end IS NULL
                    )
                GROUP BY
                    font_associates.user_id
                ORDER BY
                    font_associates.user_id
            ;"
            );

            $totalUnpaid = DB::select(
                "SELECT
                    font_associates.user_id,
                    Sum(((sales_data.package_pure_income + sales_data.license_pure_income - sales_data.affiliate_earn) / 100) * font_associates.percent) as total
                FROM
                    font_associates
                    LEFT JOIN sales_data ON sales_data.font_id = font_associates.font_id

                    WHERE font_associates.deleted_at IS NULL
                     AND sales_data.date >= font_associates.date_start
                    AND (
                    sales_data.date <= font_associates.date_end
                    OR font_associates.date_end IS NULL
                    )
                    AND sales_data.designer_paid = 0
                GROUP BY
                    font_associates.user_id
                ORDER BY
                    font_associates.user_id;"
            );


            $totalThisMonth = DB::select(
                "SELECT
                        font_associates.user_id,
                        Sum(((sales_data.package_pure_income + sales_data.license_pure_income - sales_data.affiliate_earn) / 100) * font_associates.percent) as total,
                        SUM(package_count + license_count) as totalCount
                    FROM
                        font_associates
                        LEFT JOIN sales_data ON sales_data.font_id = font_associates.font_id

                        WHERE font_associates.deleted_at IS NULL
                         AND sales_data.date >= font_associates.date_start
                        AND (
                        sales_data.date <= font_associates.date_end
                        OR font_associates.date_end IS NULL
                        )
                        AND sales_data.date >= '{$firstDayOfThisMonth}'
                    GROUP BY
                        font_associates.user_id
                    ORDER BY
                        font_associates.user_id
                    ;"
            );

            $totalLastMonth = DB::select(
                "SELECT
                        font_associates.user_id,
                        Sum(((sales_data.package_pure_income + sales_data.license_pure_income - sales_data.affiliate_earn) / 100) * font_associates.percent) as total,
                        SUM(package_count + license_count) as totalCount
                    FROM
                        font_associates
                        LEFT JOIN sales_data ON sales_data.font_id = font_associates.font_id

                        WHERE font_associates.deleted_at IS NULL
                        AND sales_data.date >= font_associates.date_start
                        AND (
                        sales_data.date <= font_associates.date_end
                        OR font_associates.date_end IS NULL
                        )
                        AND sales_data.date >= '{$firstDayOfLastMonth}'
                        AND sales_data.date < '{$firstDayOfThisMonth}'
                    GROUP BY
                        font_associates.user_id
                    ORDER BY
                        font_associates.user_id
                    ;"
            );


            $totalMap = collect($total)->keyBy('user_id');
            $totalUnpaidMap = collect($totalUnpaid)->keyBy('user_id');
            $totalLastMonthMap = collect($totalLastMonth)->keyBy('user_id');
            $totalThisMonthMap = collect($totalThisMonth)->keyBy('user_id');

            foreach ($designers as $key => $designer) {
                $userId = $designer->id; // Assuming 'id' is the user_id in designers
                $designer->total = $totalMap[$userId]->total ?? 0;
                $designer->totalUnpaid = $totalUnpaidMap[$userId]->total ?? 0;
                $designer->totalLastMonth = $totalLastMonthMap[$userId]->total ?? 0;
                $designer->totalThisMonth = $totalThisMonthMap[$userId]->total ?? 0;

                $designer->salesCount = $totalMap[$userId]->totalCount ?? 0;
                $designer->salesCountLastMonth = $totalLastMonthMap[$userId]->totalCount ?? 0;
                $designer->salesCountThisMonth = $totalThisMonthMap[$userId]->totalCount ?? 0;
            }
            $withOutFontiranAssociate = DB::table('fonts')
                ->leftJoin('font_associates', function ($join) use ($firstDayOfThisMonth) {
                    $join->on('fonts.id', '=', 'font_associates.font_id')
                        ->where('font_associates.role', '=', 'font_iran')
                        ->where('font_associates.status', '=', 'active')
                        ->whereNull('font_associates.deleted_at')
                        ->where(function ($query) use ($firstDayOfThisMonth) {
                            $query->whereNull('date_end')
                                ->orWhere('date_end', '>', $firstDayOfThisMonth);
                        });
                })
                ->whereNull('fonts.deleted_at')
                ->whereNull('font_associates.id')
                ->select('fonts.id')
                ->get()->pluck('id')->toArray();

            $adminsTotalUnpaid = SalesData::select(
                DB::raw('
                (Sum((sales_data.package_pure_income + sales_data.license_pure_income - sales_data.affiliate_earn))) as totalUnpaid
                '))
                ->where('designer_paid', '=', 0)
                ->whereNotIn('font_id', $withOutFontiranAssociate)
                ->first();

            $adminsTotal = SalesData::select(
                DB::raw('(SUM((package_pure_income - affiliate_earn + license_pure_income) )) as total'))
                ->whereNotIn('font_id', $withOutFontiranAssociate)
                ->first();

            $adminsThisMonth = SalesData::select(
                DB::raw('(Sum((sales_data.package_pure_income + sales_data.license_pure_income - sales_data.affiliate_earn) )) as totalThisMonth'))
                ->where('date', '>=', $firstDayOfThisMonth)
                ->whereNotIn('font_id', $withOutFontiranAssociate)
                ->first();

            $adminsLastMonth = SalesData::select(
                DB::raw('(Sum((sales_data.package_pure_income + sales_data.license_pure_income - sales_data.affiliate_earn) )) as totalLastMonth'))
                ->where('date', '>=', $firstDayOfLastMonth)
                ->where('date', '<', $firstDayOfThisMonth)
                ->whereNotIn('font_id', $withOutFontiranAssociate)
                ->first();

            $techAdmin = new User();
            $techAdmin->id = config('fontiran.techAdmin.id');
            $techAdmin->name = config('fontiran.techAdmin.name');
            $techAdmin->email = config('fontiran.techAdmin.email');
            $techAdmin->info = (object)config('fontiran.techAdmin.info');
            $tPercent = floatval(config('fontiran.techAdmin.percent')) / 100;
            $techAdmin->total = ($adminsTotal->total * $tPercent);
            $techAdmin->totalUnpaid = ($adminsTotalUnpaid->totalUnpaid * $tPercent);
            $techAdmin->totalLastMonth = ($adminsLastMonth->totalLastMonth * $tPercent);
            $techAdmin->totalThisMonth = ($adminsThisMonth->totalThisMonth * $tPercent);

            $productAdmin = new User();
            $productAdmin->id = config('fontiran.productAdmin.id');
            $productAdmin->name = config('fontiran.productAdmin.name');
            $productAdmin->email = config('fontiran.productAdmin.email');
            $productAdmin->info = (object)config('fontiran.productAdmin.info');
            $pPercent = floatval(config('fontiran.productAdmin.percent')) / 100;
            $productAdmin->total = ($adminsTotal->total * $pPercent);
            $productAdmin->totalUnpaid = ($adminsTotalUnpaid->totalUnpaid * $pPercent);
            $productAdmin->totalLastMonth = ($adminsLastMonth->totalLastMonth * $pPercent);
            $productAdmin->totalThisMonth = ($adminsThisMonth->totalThisMonth * $pPercent);

            $support = new User();
            $support->id = config('fontiran.support.id');
            $support->name = config('fontiran.support.name');
            $support->email = config('fontiran.support.email');
            $support->info = (object)config('fontiran.support.info');
            $sPercent = floatval(config('fontiran.support.percent')) / 100;
            $support->total = ($adminsTotal->total * $sPercent);
            $support->totalUnpaid = ($adminsTotalUnpaid->totalUnpaid * $sPercent);
            $support->totalLastMonth = ($adminsLastMonth->totalLastMonth * $sPercent);
            $support->totalThisMonth = ($adminsThisMonth->totalThisMonth * $sPercent);


            $legal = new User();
            $legal->id = config('fontiran.legal.id');
            $legal->name = config('fontiran.legal.name');
            $legal->email = config('fontiran.legal.email');
            $legal->info = (object)config('fontiran.legal.info');
            $lPercent = floatval(config('fontiran.legal.percent')) / 100;
            $legal->total = ($adminsTotal->total * $lPercent);
            $legal->totalUnpaid = ($adminsTotalUnpaid->totalUnpaid * $lPercent);
            $legal->totalLastMonth = ($adminsLastMonth->totalLastMonth * $lPercent);
            $legal->totalThisMonth = ($adminsThisMonth->totalThisMonth * $lPercent);

            $fontiran = $designers->where('id', 1)->first();
            $totalToMinus = $productAdmin->total + $techAdmin->total + $support->total + $legal->total;
            $totalUnpaidToMinus = $productAdmin->totalUnpaid + $techAdmin->totalUnpaid + $support->totalUnpaid + $legal->totalUnpaid;
            $totalLastMonthToMinus = $productAdmin->totalLastMonth + $techAdmin->totalLastMonth + $support->totalLastMonth + $legal->totalLastMonth;
            $totalThisMonthToMinus = $productAdmin->totalThisMonth + $techAdmin->totalThisMonth + $support->totalThisMonth + $legal->totalThisMonth;
            $fontiran->total -= $totalToMinus;
            $fontiran->totalUnpaid -= $totalUnpaidToMinus;
            $fontiran->totalLastMonth -= $totalLastMonthToMinus;
            $fontiran->totalThisMonth -= $totalThisMonthToMinus;


            $designers = $designers->sortByDesc('total');
            $designers->splice(1, 0,
                [$techAdmin, $productAdmin, $support, $legal]
            );
        }

        //for text of the pay button
        $lastMonthInPersian = verta()->day(1)->subMonth(1)->format('%B');

        $designerEarn = DesignerEarn::where('created_at', '>=', $firstDayOfThisMonth)->get()->first();
        // dd($designers[46]->totalUnpaid);

        return view('admin.accounting.sales-report.designer.index', compact('designers', 'dateStart', 'dateEnd', 'lastMonthInPersian', 'designerEarn'));
    }

    /**
     * Font sales reports for a designer
     *
     * @param App\Http\Requests\Admin\SearchDesignerFontSalesReportRequest $request
     * @return View|\never
     */
    public function designerFontSalesReport(SearchDesignerFontSalesReportRequest $request)
    {
        if (!auth()->user()->can('can-view-designer-fonts-sales-report')) return abort(403);

        $designer = Auth::user();

        //============================declaring dates
        //now in shamsi

        //last month in shamsi

        //first day of last shamsi month in gregorian
        $firstDayOfLastMonth = getGregorian(Verta::createJalali(verta()->day(1)->subMonth()->year, verta()->day(1)->subMonth()->month, 1));

        //last day of last shamsi month in gregorian
        $lastDayOfLastMonth = getGregorian(Verta::createJalali(verta()->day(1)->subMonth()->year, verta()->day(1)->subMonth()->month, verta()->day(1)->subMonth()->daysInMonth));

        //now in gregorian
        $gregorianNow = now()->format('Y-m-d');

        //first day of this shamsi month in gregorian
        $firstDayOfThisMonth = getGregorian(Verta::createJalali(verta()->day(1)->year, verta()->day(1)->month, 1));
        //==========================================

        //============================for search
        $dateStart = $request->date_start;
        $dateEnd = $request->date_end;

        $dateStartGregorian = null;
        $dateEndGregorian = null;

        if ($request->date_start != null) $dateStartGregorian = getGregorian($request->date_start);
        if ($request->date_end != null) $dateEndGregorian = Carbon::parse(getGregorian($request->date_end))->addDay()->format('Y-m-d');
        //=======================================

        $associate = Associate::find($request->associate);
        $font = $associate->font;

        $data = collect();

        $associateEnd = now();
        if ($associate->date_end != null) $associateEnd = $associate->date_end;
        $totalData = SalesData::where('font_id', $font->id)
            ->where('date', '>=', $associate->date_start)
            ->where('date', '<=', $associateEnd);

        // ==================================applying date search
        if ($dateStartGregorian != null) {
            $totalData = $totalData->where('date', '>=', $dateStartGregorian);
        }

        if ($dateEndGregorian != null) {
            $totalData = $totalData->where('date', '<', $dateEndGregorian);
        }
        //========================================================

        $totalData = $totalData
        ->where(function ($query) {

            $query->orWhere(function ($query2) {
                $query2->where('sales_data.package_pure_income', '>', 0)
                        ->where('sales_data.package_income', '>', 0);
            })->orWhere(function ($query2) {
                $query2->where('sales_data.license_income', '>', 0)
                ->where('sales_data.license_pure_income', '>', 0);
            });
        })
        ->select(
            DB::raw('SUM(package_income) as package_income'),
            DB::raw('SUM(package_pure_income - affiliate_earn) as package_pure_income'),
            DB::raw('SUM(package_count) as package_count'),
            DB::raw('SUM(license_income) as license_income'),
            DB::raw('SUM(license_pure_income) as license_pure_income'),
            // DB::raw('SUM(license_count) as license_count'),
            DB::raw('SUM(CASE WHEN license_income <= 0 THEN 0 ELSE license_count END) as license_count'),

            DB::raw('SUM(package_discount) as package_discount'),
            DB::raw('SUM(license_discount) as license_discount'),
            DB::raw('SUM(package_coupon_discount) as package_coupon_discount'),
            DB::raw('SUM(license_coupon_discount) as license_coupon_discount'),
            DB::raw('SUM(package_group_discount) as package_group_discount'),
            DB::raw('SUM(license_group_discount) as license_group_discount'),
            DB::raw('SUM(previous_package_discount) as previous_package_discount'),
            DB::raw('SUM(previous_license_discount) as previous_license_discount'),
            DB::raw('SUM(affiliate_earn) as affiliate_earn'),
            DB::raw('SUM(affiliate_coupon_total) as affiliate_coupon_total')
        )
            ->get()[0];

        $totalThisMonth = SalesData::where('font_id', $font->id)
            ->where('date', '>=', $firstDayOfThisMonth)
            ->select(
                DB::raw('SUM(package_pure_income - affiliate_earn + license_pure_income) as amount'),
            )
            ->where(function ($query) {

                $query->orWhere(function ($query2) {
                    $query2->where('sales_data.package_pure_income', '>', 0)
                            ->where('sales_data.package_income', '>', 0);
                })->orWhere(function ($query2) {
                    $query2->where('sales_data.license_income', '>', 0)
                    ->where('sales_data.license_pure_income', '>', 0);
                });
            })
            ->get()[0];

        $totalLastMonth = SalesData::where('font_id', $font->id)
            ->where('date', '>=', $firstDayOfLastMonth)
            ->where('date', '<', $firstDayOfThisMonth)
            ->select(
                DB::raw('SUM(package_pure_income - affiliate_earn + license_pure_income) as amount'),
            )
            ->where(function ($query) {

                $query->orWhere(function ($query2) {
                    $query2->where('sales_data.package_pure_income', '>', 0)
                            ->where('sales_data.package_income', '>', 0);
                })->orWhere(function ($query2) {
                    $query2->where('sales_data.license_income', '>', 0)
                    ->where('sales_data.license_pure_income', '>', 0);
                });
            })
            ->get()[0];


        //=======================================getting sales data
        $data = collect();
        //===================================counts
        $data->fontCount = (int)$totalData->package_count;
        $data->licenseCount = (int)$totalData->license_count;
        //=========================================

        //====================================income
        $data->fontIncome = (int)(($totalData->package_pure_income / 100) * $associate->percent);
        $data->licenseIncome = (int)(($totalData->license_pure_income / 100) * $associate->percent);
        $data->totalIncome = $data->fontIncome + $data->licenseIncome;
        //==========================================

        //=====================================total income of this month
        $data->totalIncomeThisMonth = (int)(($totalThisMonth->amount / 100) * $associate->percent);
        //===============================================================

        //=====================================total income of last month
        $data->totalIncomeLastMonth = (int)(($totalLastMonth->amount / 100) * $associate->percent);
        //===============================================================

        //======================================income without discount
        $data->fontDiscountLessIncome = (int)(($totalData->package_income / 100) * $associate->percent);
        $data->licenseDiscountLessIncome = (int)(($totalData->license_income / 100) * $associate->percent);
        $data->totalDiscountLessIncome = $data->fontDiscountLessIncome + $data->licenseDiscountLessIncome;
        //=============================================================

        //======================================coupon discount
        $data->fontCouponDiscount = (int)(($totalData->package_coupon_discount / 100) * $associate->percent);
        $data->licenseCouponDiscount = (int)(($totalData->license_coupon_discount / 100) * $associate->percent);
        //=====================================================

        //======================================group discount
        $data->fontGroupDiscount = (int)(($totalData->package_group_discount / 100) * $associate->percent);
        $data->licenseGroupDiscount = (int)(($totalData->license_group_discount / 100) * $associate->percent);
        //====================================================

        //======================================discount
        $data->fontDiscount = (int)(($totalData->package_discount / 100) * $associate->percent);
        $data->licenseDiscount = (int)(($totalData->license_discount / 100) * $associate->percent);
        //==============================================

        //======================================affiliate earn
        $data->affiliateEarn = (int)(($totalData->affiliate_earn / 100) * $associate->percent);
        //====================================================

        //======================================affiliate earn
        $data->affiliateDiscount = (int)(($totalData->affiliate_coupon_total / 100) * $associate->percent);
        //====================================================

        //======================================previous package and license discount
        $data->previousPackageDiscount = (int)($totalData->previous_package_discount);
        $data->previousLicenseDiscount = (int)($totalData->previous_license_discount);
        //==============================================
        //=========================================================

        //=======================================package outcome
        $pData = array();
        foreach ($font->packages as $package) {
            $package->outcome = (int)DB::select("SELECT SUM(package_price - affiliate_earn) as value FROM cart_packages WHERE package_id = " . $package->id)[0]->value;

            array_push($pData, ['value' => $package->outcome, 'category' => $package->title]);
        }
        $pData = collect($pData)->sortByDesc('value');

        //sorting by value
        $packageData = array();
        foreach ($pData as $package) {
            if ($associate->percent > 0) {
                array_push($packageData, ['value' => $package['value'], 'category' => $package['category']]);
            }
        }
        //======================================================
        return view('admin.accounting.sales-report.font.designer.font', compact('font', 'associate', 'data', 'packageData', 'dateStart', 'dateEnd'));
    }

    /**
     * sales reports of a designer fonts
     *
     * @param App\Http\Requests\Admin\SearchDesignerFontsSalesReportRequest $request
     * @return View|\never
     */
    public function designerAllFontSalesReport(SearchDesignerFontsSalesReportRequest $request)
    {
        setCustomDBConfig();
        if (!auth()->user()->can('can-view-designer-fonts-sales-report')) return abort(403);

        $designer = Auth::user();

        //============================declaring dates
        //now in shamsi

        //last month in shamsi
        //first day of last shamsi month in gregorian
        $firstDayOfLastMonth = getGregorian(Verta::createJalali(verta()->day(1)->subMonth(1)->year, verta()->day(1)->subMonth(1)->month, 1));

        //first day of this shamsi month in gregorian
        $firstDayOfThisMonth = getGregorian(Verta::createJalali(verta()->day(1)->year, verta()->day(1)->month, 1));
        //==========================================

        //============================for search
        $dateStart = $request->date_start;
        $dateEnd = $request->date_end;

        $dateStartGregorian = null;
        $dateEndGregorian = null;

        if ($request->date_start != null) $dateStartGregorian = getGregorian($request->date_start);
        if ($request->date_end != null) $dateEndGregorian = Carbon::parse(getGregorian($request->date_end))->addDay()->format('Y-m-d');
        //=======================================

        //associations of this designer
        $associations = Associate::leftJoin('sales_data', function ($join) use ($dateStartGregorian, $dateEndGregorian) {
            $join->on('font_associates.font_id', '=', 'sales_data.font_id');
            $join->on('sales_data.date', '>=', 'font_associates.date_start');
            $join->on(DB::raw('( sales_data.date <= font_associates.date_end OR font_associates.date_end IS NULL )'), 'IS', DB::raw('TRUE'));

            // ==================================applying date search
            if ($dateStartGregorian != null) {
                $join->on('sales_data.date', '>=', DB::Raw("'{$dateStartGregorian}'"));
            }

            if ($dateEndGregorian != null) {
                $join->on('sales_data.date', '<', DB::Raw("'{$dateEndGregorian}'"));
            }
            //========================================================
        });

        $associations = $associations->where('font_associates.user_id', $designer->id)
            ->whereNull('font_associates.deleted_at')
            ->select(
                'font_associates.id',
                'font_associates.role',
                'font_associates.show',
                'font_associates.percent',
                'font_associates.status',
                'font_associates.date_start',
                'font_associates.date_end',
                'font_associates.font_id',
                DB::raw('Sum(((sales_data.package_pure_income + sales_data.license_pure_income - sales_data.affiliate_earn) / 100) * font_associates.percent) as totalValue')
            )
            ->groupBy(
                'font_associates.id',

            )
            ->orderBy('font_associates.id')
            ->get();
        //associations of this designer
        $associationsLastMonth = DB::table('font_associates')->leftJoin('sales_data', function ($join) use ($firstDayOfLastMonth, $firstDayOfThisMonth) {
            $join->on('font_associates.font_id', '=', 'sales_data.font_id');
            $join->on('sales_data.date', '>=', 'font_associates.date_start');
            $join->on(DB::raw('( sales_data.date <= font_associates.date_end OR font_associates.date_end IS NULL AND font_associates.deleted_at IS NULL )'), 'IS', DB::raw('TRUE'));
            $join->on('sales_data.date', '>=', DB::Raw("'{$firstDayOfLastMonth}'"));
            $join->on('sales_data.date', '<', DB::Raw("'{$firstDayOfThisMonth}'"));
        })->where('font_associates.user_id', $designer->id)
            ->whereNull('font_associates.deleted_at')
            ->select(
                'font_associates.id',
                DB::raw('Sum(((sales_data.package_pure_income + sales_data.license_pure_income - sales_data.affiliate_earn) / 100) * font_associates.percent) as lastMonthValue')
            )
            ->groupBy(
                'font_associates.id',
            )
            ->orderBy('font_associates.id')
            ->get();

        //merging associates with associate last month
        foreach ($associations as $association) {
            $associationLastMonth = $associationsLastMonth->where('id', $association->id)->first();
            if ($associationLastMonth) {
                $association->lastMonthValue = $associationLastMonth->lastMonthValue;
            }
        }

        $totalData = DB::table('font_associates')->leftJoin('sales_data', function ($join) use ($dateStartGregorian, $dateEndGregorian) {
            $join->on('font_associates.font_id', '=', 'sales_data.font_id');
            $join->on('sales_data.date', '>=', 'font_associates.date_start');
            $join->on(DB::raw('( sales_data.date <= font_associates.date_end OR font_associates.date_end IS NULL )'), 'IS', DB::raw('TRUE'));

            // ==================================applying date search
            if ($dateStartGregorian != null) {
                $join->on('sales_data.date', '>=', DB::Raw("'{$dateStartGregorian}'"));
            }

            if ($dateEndGregorian != null) {
                $join->on('sales_data.date', '<', DB::Raw("'{$dateEndGregorian}'"));
            }
            //========================================================
        })->where('font_associates.user_id', $designer->id)
            ->whereNull('font_associates.deleted_at');

        $totalData = $totalData
        ->where(function ($query) {

            $query->orWhere(function ($query2) {
                $query2->where('sales_data.package_pure_income', '>', 0)
                        ->where('sales_data.package_income', '>', 0);
            })->orWhere(function ($query2) {
                $query2->where('sales_data.license_income', '>', 0)
                ->where('sales_data.license_pure_income', '>', 0);
            });
        })
        ->whereNull('font_associates.deleted_at')
            ->select(
                DB::raw('(SUM(((package_pure_income - affiliate_earn + license_pure_income)/100)*font_associates.percent)) as totalIncome'),
                DB::raw('(SUM(((package_income + license_income)/100)*font_associates.percent)) as totalDiscountLessIncome'),
                DB::raw('(SUM(((package_pure_income - affiliate_earn)/100)*font_associates.percent)) as fontIncome'),
                DB::raw('(SUM(((package_income)/100)*font_associates.percent)) as fontDiscountLessIncome'),
                DB::raw('SUM(package_count) as fontCount'),
                DB::raw('(SUM((license_pure_income/100)*font_associates.percent)) as licenseIncome'),
                DB::raw('(SUM((license_income/100)*font_associates.percent)) as licenseDiscountLessIncome'),
                DB::raw('SUM(CASE WHEN license_income <= 0 THEN 0 ELSE license_count END) as licenseCount'),
                DB::raw('(SUM((package_discount/100)*font_associates.percent ))as fontDiscount'),
                DB::raw('(SUM((license_discount/100)*font_associates.percent)) as licenseDiscount'),
                DB::raw('(SUM((package_coupon_discount/100)*font_associates.percent)) as fontCouponDiscount'),
                DB::raw('(SUM((license_coupon_discount/100)*font_associates.percent)) as licenseCouponDiscount'),
                DB::raw('(SUM((package_group_discount/100)*font_associates.percent)) as fontGroupDiscount'),
                DB::raw('(SUM((license_group_discount/100)*font_associates.percent)) as licenseGroupDiscount'),
                DB::raw('(SUM((previous_package_discount/100)*font_associates.percent)) as previousPackageDiscount'),
                DB::raw('(SUM((previous_license_discount/100)*font_associates.percent)) as previousLicenseDiscount'),
                DB::raw('(SUM((affiliate_earn/100)*font_associates.percent)) as affiliateEarn'),
                DB::raw('(SUM((affiliate_coupon_total/100)*font_associates.percent)) as affiliateDiscount')
            )->get()[0];

        $totalData->totalIncomeThisMonth = DB::table('font_associates')->join('sales_data', function ($join) {
            $join->on('font_associates.font_id', '=', 'sales_data.font_id');
            $join->on('sales_data.date', '>=', 'font_associates.date_start');
            $join->on(DB::raw('( sales_data.date <= font_associates.date_end OR font_associates.date_end IS NULL )'), 'IS', DB::raw('TRUE'));
        })->where('font_associates.user_id', $designer->id)
            ->whereNull('font_associates.deleted_at')
            ->where('sales_data.date', '>=', (string)($firstDayOfThisMonth))
            ->select(
                DB::raw('(SUM(((package_income + license_income)/100)*font_associates.percent)) as totalDiscountLessIncome')
            )->get()[0]->totalDiscountLessIncome;

        $totalData->totalIncomeLastMonth = DB::table('font_associates')->join('sales_data', function ($join) use ($firstDayOfLastMonth, $firstDayOfThisMonth) {
            $join->on('font_associates.font_id', '=', 'sales_data.font_id');
            $join->on('sales_data.date', '>=', 'font_associates.date_start');
            $join->on(DB::raw('( sales_data.date <= font_associates.date_end OR font_associates.date_end IS NULL )'), 'IS', DB::raw('TRUE'));
            $join->on('sales_data.date', '>=', DB::raw("'{$firstDayOfLastMonth}'"));
            $join->on('sales_data.date', '<', DB::raw("'{$firstDayOfThisMonth}'"));
        })
            ->where('font_associates.user_id', $designer->id)
            ->whereNull('font_associates.deleted_at')
            ->select(
                DB::raw('(SUM(((package_pure_income- affiliate_earn + license_pure_income)/100)*font_associates.percent)) as totalDiscountLessIncome'),
            )
            ->get()[0]->totalDiscountLessIncome;

        $totalData->totalPureIncomeThisMonth = DB::table('font_associates')->join('sales_data', function ($join) {
            $join->on('font_associates.font_id', '=', 'sales_data.font_id');
            $join->on('sales_data.date', '>=', 'font_associates.date_start');
            $join->on(DB::raw('( sales_data.date <= font_associates.date_end OR font_associates.date_end IS NULL )'), 'IS', DB::raw('TRUE'));
        })->where('font_associates.user_id', $designer->id)
            ->whereNull('font_associates.deleted_at')
            ->where('sales_data.date', '>=', (string)($firstDayOfThisMonth))
            ->select(
                DB::raw('(SUM(((package_pure_income- affiliate_earn + license_pure_income)/100)*font_associates.percent)) as totalDiscountLessIncome')
            )->first()->totalDiscountLessIncome;


        return view('admin.accounting.sales-report.font.designer.all-fonts', compact('totalData', 'associations', 'dateStart', 'dateEnd'));
    }

    /**
     * makes 'designer_paid' of all last month pays true
     *
     */
    public function payDesigners(Request $request)
    {
        DB::beginTransaction();

        //on lack of permission
        if (!auth()->user()->can('can-edit-cart')) return abort(403);

        //now in shamsi
        $now = verta();
        $now->day = 1;

        //last month in shamsi
        $lastMonth = $now->subMonth(1);

        //first day of last shamsi month in gregorian
        $firstDayOfLastMonth = Carbon::parse(getGregorian(Verta::createJalali($lastMonth->year, $lastMonth->month, 1)));

        //last day of last shamsi month in gregorian
        $firstDayOfThisMonth = Carbon::parse(getGregorian(Verta::createJalali($now->year, $now->month, 1)));

        $thisMonth = $now->subMonth(1)->format('%B');

        $designerEarn = DesignerEarn::where('created_at', '>=', $firstDayOfThisMonth)->get()->first();
        if (!$designerEarn) {
            $designers = User::role('designer')->get();

            try {
                foreach ($designers as $designer) {

                    //associations of this designer
                    $associateData = Associate::where('user_id', $designer->id)
                        ->whereNull('deleted_at')
                        ->where(function ($query) use ($firstDayOfThisMonth) {
                            $query->whereNull('date_end')
                                ->orWhere('date_end', '>', $firstDayOfThisMonth);
                        })
                        ->get();

                    $totalUnpaid = 0;
                    $cartIds = array();

                    //for each association
                    foreach ($associateData as $data) {
                        //date end for associate
                        $dateEnd = now();
                        if ($data->date_end != null) $dateEnd = Carbon::parse($data->date_end);

                        $dateEnd->addDay();

                        $cartPackages = CartPackage::join('carts', 'cart_packages.cart_id', '=', 'carts.id')
                            ->where('carts.status', 'success')
                            ->where('carts.designer_paid', false)
                            ->where('cart_packages.font_id', $data->font_id)
                            ->where('carts.updated_at', '>=', $data->date_start)
                            ->where('carts.updated_at', '<', $dateEnd)
                            ->where('carts.updated_at', '>=', $firstDayOfLastMonth)
                            ->where('carts.updated_at', '<', $firstDayOfThisMonth)
                            //cart_packages.package_price - cart_packages.affiliate_earn so we can get the designer's earn
                            ->select(DB::raw('cart_packages.package_price - cart_packages.affiliate_earn as package_price'), 'carts.id')
                            ->get();

                        $cartLicenses = CartLicense::join('carts', 'cart_licenses.cart_id', '=', 'carts.id')
                            ->where('carts.status', 'success')
                            ->where('carts.designer_paid', false)
                            ->where('cart_licenses.font_id', $data->font_id)
                            ->where('carts.updated_at', '>=', $data->date_start)
                            ->where('carts.updated_at', '<', $dateEnd)
                            ->where('carts.updated_at', '>=', $firstDayOfLastMonth)
                            ->where('carts.updated_at', '<', $firstDayOfThisMonth)
                            ->select('cart_licenses.license_price', 'carts.id')
                            ->get();

                        //total earn of this designer
                        $totalUnpaid += (($cartPackages->sum('package_price') / 100) * $data->percent);
                        $totalUnpaid += (($cartLicenses->sum('license_price') / 100) * $data->percent);

                        //saving cart ids
                        // array_push($cartIds, $cartPackages->pluck('id')->toArray());
                        // array_push($cartIds, $cartLicenses->pluck('id')->toArray());
                    }
                    $exchangeRate = Str::replace(",", '', $request->exchange_rate);
                    if ($totalUnpaid != 0) {
                        _logger($totalUnpaid);
                        _logger($exchangeRate);

                        DesignerEarn::create([
                            'user_id' => $designer->id,
                            'cart_ids' => '',
                            'amount' => $totalUnpaid,
                            'rial_amount' => (float)$totalUnpaid * (float)$exchangeRate,
                            'exchange_rate' => $exchangeRate,
                            'description' => "تسویه حساب {$thisMonth} ماه " . $now->subMonth(1)->year
                        ]);
                    }
                }

                DB::unprepared(DB::raw("UPDATE carts
                    set designer_paid = 1
                    where updated_at >= '{$firstDayOfLastMonth->format('Y-m-d')}'
                    and updated_at < '{$firstDayOfThisMonth->format('Y-m-d')}'
                    and status = 'success';"));

                DB::commit();

                $updateSalesData = new UpdateSalesData();
                $updateSalesData->update($firstDayOfLastMonth, $firstDayOfThisMonth);
            } catch (Exception $e) {
                Log::error($e);
                DB::rollBack();
                throw ValidationException::withMessages((array)$e->getMessage());
            }
        }
    }
}
